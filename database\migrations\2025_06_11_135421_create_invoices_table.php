<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parcel_id')->constrained()->onDelete('cascade');
            $table->string('invoice_number')->unique();
            $table->string('issued_to_type')->comment('e.g., individual, company, sender, receiver');
            $table->string('issued_to_name');
            $table->string('issued_to_address')->nullable();
            $table->string('issued_to_email')->nullable();
            $table->decimal('amount', 10, 2)->comment('Total amount of the invoice');
            $table->date('issue_date')->comment('Date when the invoice was issued');
            $table->date('due_date')->comment('Date when the invoice is due');
            $table->text('notes')->nullable()->comment('Additional notes or comments regarding the invoice');
            $table->decimal('sub_total', 10, 2);
            $table->decimal('tax_amount', 10, 2)->default(0.00);
            $table->decimal('total_amount', 10, 2);
            $table->string('status')->default('draft'); // e.g., draft, sent, paid, overdue, cancelled
            $table->text('payment_instructions')->nullable();
            $table->foreignId('created_by_id')->nullable()->constrained('users')->onDelete('set null'); // Admin who created it
            $table->boolean('is_sent')->default(false)->comment('Indicates if the invoice has been sent to the customer');
            $table->boolean('is_deleted')->default(false)->comment('Indicates if the invoice is deleted (soft delete)');
            $table->foreignId('deleted_by_id')->nullable()->constrained('users')->onDelete('set null'); // Admin who deleted it
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
