<?php $__env->startSection('title', 'Shopping Cart'); ?>

<?php $__env->startSection('content'); ?>
<!-- Cart Header -->
<section class="py-12 bg-gradient-to-r from-green-600 to-green-700">
    <div class="container mx-auto px-4">
        <div class="text-center text-white">
            <h1 class="text-4xl font-bold mb-4">Shopping Cart</h1>
            <p class="text-xl opacity-90">Review your items and proceed to checkout</p>
        </div>
    </div>
</section>

<!-- Cart Content -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <?php if($cart->isEmpty()): ?>
            <!-- Empty Cart -->
            <div class="text-center py-16">
                <div class="mb-8">
                    <i class="fas fa-shopping-cart text-6xl text-gray-300"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
                <p class="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                    Continue Shopping
                </a>
            </div>
        <?php else: ?>
            <!-- Cart Items -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Cart Items List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Cart Items (<?php echo e($cart->item_count); ?>)</h2>
                        </div>
                        
                        <div class="divide-y divide-gray-200">
                            <?php $__currentLoopData = $cart->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="p-6 cart-item" data-product-id="<?php echo e($item->product_id); ?>">
                                <div class="flex items-center space-x-4">
                                    <!-- Product Image -->
                                    <div class="flex-shrink-0">
                                        <img src="<?php echo e($item->product->featured_image_url ?? 'https://via.placeholder.com/80x80?text=No+Image'); ?>" 
                                             alt="<?php echo e($item->product->name); ?>" 
                                             class="w-20 h-20 object-cover rounded-lg">
                                    </div>
                                    
                                    <!-- Product Details -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                            <a href="<?php echo e(route('products.show', $item->product)); ?>" class="hover:text-green-600">
                                                <?php echo e($item->product->name); ?>

                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-600 mb-2">SKU: <?php echo e($item->product->sku); ?></p>
                                        <p class="text-lg font-bold text-green-600"><?php echo \App\Helpers\CurrencyHelper::format($item->unit_price); ?></p>
                                    </div>
                                    
                                    <!-- Quantity Controls -->
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center border border-gray-300 rounded-lg">
                                            <button onclick="updateCartQuantity(<?php echo e($item->product_id); ?>, <?php echo e($item->quantity - 1); ?>)" 
                                                    class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                            <span class="px-4 py-2 text-center min-w-[3rem]"><?php echo e($item->quantity); ?></span>
                                            <button onclick="updateCartQuantity(<?php echo e($item->product_id); ?>, <?php echo e($item->quantity + 1); ?>)" 
                                                    class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- Item Total -->
                                        <div class="text-right min-w-[6rem]">
                                            <p class="text-lg font-bold text-gray-900"><?php echo \App\Helpers\CurrencyHelper::format($item->total_price); ?></p>
                                        </div>
                                    </div>
                                    
                                    <!-- Actions -->
                                    <div class="flex flex-col space-y-2">
                                        <button onclick="saveForLater(<?php echo e($item->product_id); ?>)" 
                                                class="text-sm text-blue-600 hover:text-blue-800">
                                            Save for Later
                                        </button>
                                        <button onclick="removeFromCart(<?php echo e($item->product_id); ?>)" 
                                                class="text-sm text-red-600 hover:text-red-800">
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <!-- Cart Actions -->
                        <div class="p-6 bg-gray-50 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <button onclick="clearCart()" 
                                        class="text-red-600 hover:text-red-800 font-medium">
                                    Clear Cart
                                </button>
                                <a href="<?php echo e(route('products.index')); ?>" 
                                   class="text-green-600 hover:text-green-800 font-medium">
                                    Continue Shopping
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Cart Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-lg p-6 sticky top-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Order Summary</h3>
                        
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Subtotal</span>
                                <span class="font-semibold"><?php echo \App\Helpers\CurrencyHelper::format($cart->subtotal); ?></span>
                            </div>

                            <?php if($cart->tax_amount > 0): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Tax</span>
                                <span class="font-semibold"><?php echo \App\Helpers\CurrencyHelper::format($cart->tax_amount); ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if($cart->shipping_amount > 0): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-semibold"><?php echo \App\Helpers\CurrencyHelper::format($cart->shipping_amount); ?></span>
                            </div>
                            <?php else: ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Shipping</span>
                                <span class="font-semibold text-green-600">Free</span>
                            </div>
                            <?php endif; ?>

                            <?php if($cart->discount_amount > 0): ?>
                            <div class="flex justify-between text-green-600">
                                <span>Discount</span>
                                <span class="font-semibold">-<?php echo \App\Helpers\CurrencyHelper::format($cart->discount_amount); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4 mb-6">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total</span>
                                <span class="text-green-600"><?php echo \App\Helpers\CurrencyHelper::format($cart->total_amount); ?></span>
                            </div>
                        </div>
                        
                        <!-- Checkout Button -->
                        <a href="<?php echo e(route('checkout.index')); ?>" 
                           class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold text-center block transition-colors">
                            Proceed to Checkout
                        </a>
                        
                        <!-- Security Badge -->
                        <div class="mt-6 text-center">
                            <div class="flex items-center justify-center text-sm text-gray-600">
                                <i class="fas fa-lock mr-2 text-green-600"></i>
                                Secure Checkout
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Update cart item quantity
function updateCartQuantity(productId, quantity) {
    fetch('/cart/update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to update totals
            window.location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Remove item from cart
function removeFromCart(productId) {
    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }
    
    fetch('/cart/remove', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove item from DOM or reload page
            window.location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Save item for later (move to wishlist)
function saveForLater(productId) {
    fetch('/cart/save-for-later', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload page to update cart
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}

// Clear entire cart
function clearCart() {
    if (!confirm('Are you sure you want to clear your entire cart?')) {
        return;
    }
    
    fetch('/cart/clear', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Reload page
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/frontend/cart/index.blade.php ENDPATH**/ ?>