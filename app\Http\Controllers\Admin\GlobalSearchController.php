<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Parcel;
use App\Models\Product;
use App\Models\Category;
use App\Models\Career;
use App\Models\JobApplication;
use App\Models\BlogPost;
use App\Models\Contact;
use App\Models\NewsletterSubscriber;
use App\Models\Carrier;
use App\Models\QuoteRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class GlobalSearchController extends Controller
{
    /**
     * Perform global search across all entities
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', '');
            $limit = $request->get('limit', 50);

            if (strlen($query) < 2) {
                return response()->json([
                    'results' => [],
                    'total' => 0,
                    'message' => 'Please enter at least 2 characters to search'
                ]);
            }

            $results = [];

            // Search Users
            try {
                $users = $this->searchUsers($query, $limit);
                $results = array_merge($results, $users);
            } catch (\Exception $e) {
                \Log::error('Global search - Users error: ' . $e->getMessage());
            }

            // Search Orders
            try {
                $orders = $this->searchOrders($query, $limit);
                $results = array_merge($results, $orders);
            } catch (\Exception $e) {
                \Log::error('Global search - Orders error: ' . $e->getMessage());
            }

            // Search Parcels
            try {
                $parcels = $this->searchParcels($query, $limit);
                $results = array_merge($results, $parcels);
            } catch (\Exception $e) {
                \Log::error('Global search - Parcels error: ' . $e->getMessage());
            }

            // Search Products
            try {
                $products = $this->searchProducts($query, $limit);
                $results = array_merge($results, $products);
            } catch (\Exception $e) {
                \Log::error('Global search - Products error: ' . $e->getMessage());
            }

            // Search Categories
            try {
                $categories = $this->searchCategories($query, $limit);
                $results = array_merge($results, $categories);
            } catch (\Exception $e) {
                \Log::error('Global search - Categories error: ' . $e->getMessage());
            }

            // Search Careers
            try {
                $careers = $this->searchCareers($query, $limit);
                $results = array_merge($results, $careers);
            } catch (\Exception $e) {
                \Log::error('Global search - Careers error: ' . $e->getMessage());
            }

            // Search Job Applications
            try {
                $jobApplications = $this->searchJobApplications($query, $limit);
                $results = array_merge($results, $jobApplications);
            } catch (\Exception $e) {
                \Log::error('Global search - Job Applications error: ' . $e->getMessage());
            }

            // Search Blog Posts
            try {
                $blogPosts = $this->searchBlogPosts($query, $limit);
                $results = array_merge($results, $blogPosts);
            } catch (\Exception $e) {
                \Log::error('Global search - Blog Posts error: ' . $e->getMessage());
            }

            // Search Contacts
            try {
                $contacts = $this->searchContacts($query, $limit);
                $results = array_merge($results, $contacts);
            } catch (\Exception $e) {
                \Log::error('Global search - Contacts error: ' . $e->getMessage());
            }

            // Search Newsletter Subscribers
            try {
                $subscribers = $this->searchNewsletterSubscribers($query, $limit);
                $results = array_merge($results, $subscribers);
            } catch (\Exception $e) {
                \Log::error('Global search - Newsletter Subscribers error: ' . $e->getMessage());
            }

            // Search Carriers
            try {
                $carriers = $this->searchCarriers($query, $limit);
                $results = array_merge($results, $carriers);
            } catch (\Exception $e) {
                \Log::error('Global search - Carriers error: ' . $e->getMessage());
            }

            // Search Quote Requests
            try {
                $quotes = $this->searchQuoteRequests($query, $limit);
                $results = array_merge($results, $quotes);
            } catch (\Exception $e) {
                \Log::error('Global search - Quote Requests error: ' . $e->getMessage());
            }

            // Sort results by relevance (exact matches first, then partial matches)
            usort($results, function($a, $b) use ($query) {
                $aScore = $this->calculateRelevanceScore($a, $query);
                $bScore = $this->calculateRelevanceScore($b, $query);
                return $bScore <=> $aScore;
            });

            // Limit total results
            $results = array_slice($results, 0, $limit);

            return response()->json([
                'results' => $results,
                'total' => count($results),
                'query' => $query
            ]);

        } catch (\Exception $e) {
            \Log::error('Global search error: ' . $e->getMessage());
            return response()->json([
                'results' => [],
                'total' => 0,
                'error' => 'Search temporarily unavailable'
            ], 500);
        }
    }

    /**
     * Search Users
     */
    private function searchUsers(string $query, int $limit): array
    {
        $users = User::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%")
              ->orWhere('phone', 'like', "%{$query}%")
              ->orWhere('company_name', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->limit($limit)
        ->get();

        return $users->map(function($user) {
            return [
                'id' => $user->id,
                'type' => 'user',
                'title' => $user->name,
                'subtitle' => $user->email,
                'description' => $user->role . ($user->company_name ? " • {$user->company_name}" : ''),
                'url' => route('admin.users.index') . '?search=' . urlencode($user->email),
                'icon' => 'fas fa-user',
                'color' => 'blue',
                'meta' => [
                    'role' => $user->role,
                    'phone' => $user->phone,
                    'created_at' => $user->created_at->format('M d, Y')
                ]
            ];
        })->toArray();
    }

    /**
     * Search Orders
     */
    private function searchOrders(string $query, int $limit): array
    {
        $orders = Order::where(function($q) use ($query) {
            $q->where('order_number', 'like', "%{$query}%")
              ->orWhere('customer_name', 'like', "%{$query}%")
              ->orWhere('customer_email', 'like', "%{$query}%")
              ->orWhere('customer_phone', 'like', "%{$query}%")
              ->orWhere('tracking_number', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->with('customer')
        ->limit($limit)
        ->get();

        return $orders->map(function($order) {
            return [
                'id' => $order->id,
                'type' => 'order',
                'title' => "Order #{$order->order_number}",
                'subtitle' => $order->customer_name ?: $order->customer?->name,
                'description' => "Status: {$order->status} • Total: $" . number_format($order->total_amount, 2),
                'url' => route('admin.orders.index') . '?search=' . urlencode($order->order_number),
                'icon' => 'fas fa-shopping-cart',
                'color' => 'green',
                'meta' => [
                    'status' => $order->status,
                    'total' => $order->total_amount,
                    'created_at' => $order->created_at->format('M d, Y')
                ]
            ];
        })->toArray();
    }

    /**
     * Search Parcels
     */
    private function searchParcels(string $query, int $limit): array
    {
        $parcels = Parcel::where(function($q) use ($query) {
            $q->where('tracking_number', 'like', "%{$query}%")
              ->orWhere('sender_name', 'like', "%{$query}%")
              ->orWhere('sender_email', 'like', "%{$query}%")
              ->orWhere('recipient_name', 'like', "%{$query}%")
              ->orWhere('recipient_email', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->with(['carrier', 'user'])
        ->limit($limit)
        ->get();

        return $parcels->map(function($parcel) {
            return [
                'id' => $parcel->id,
                'type' => 'parcel',
                'title' => "Parcel #{$parcel->tracking_number}",
                'subtitle' => "{$parcel->sender_name} → {$parcel->recipient_name}",
                'description' => "Status: {$parcel->status} • Carrier: {$parcel->carrier?->name}",
                'url' => route('admin.parcels.show', $parcel->id),
                'icon' => 'fas fa-box',
                'color' => 'purple',
                'meta' => [
                    'status' => $parcel->status,
                    'carrier' => $parcel->carrier?->name,
                    'created_at' => $parcel->created_at->format('M d, Y')
                ]
            ];
        })->toArray();
    }

    /**
     * Search Products
     */
    private function searchProducts(string $query, int $limit): array
    {
        $products = Product::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('sku', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%")
              ->orWhere('short_description', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->with('category')
        ->limit($limit)
        ->get();

        return $products->map(function($product) {
            return [
                'id' => $product->id,
                'type' => 'product',
                'title' => $product->name,
                'subtitle' => "SKU: {$product->sku}",
                'description' => "Category: {$product->category?->name} • Price: $" . number_format($product->price, 2),
                'url' => route('admin.ecommerce.products.edit', $product->id),
                'icon' => 'fas fa-cube',
                'color' => 'orange',
                'meta' => [
                    'sku' => $product->sku,
                    'price' => $product->price,
                    'stock' => $product->stock_quantity,
                    'status' => $product->is_active ? 'Active' : 'Inactive'
                ]
            ];
        })->toArray();
    }

    /**
     * Search Categories
     */
    private function searchCategories(string $query, int $limit): array
    {
        $categories = Category::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('slug', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->withCount('products')
        ->limit($limit)
        ->get();

        return $categories->map(function($category) {
            return [
                'id' => $category->id,
                'type' => 'category',
                'title' => $category->name,
                'subtitle' => "Slug: {$category->slug}",
                'description' => "{$category->products_count} products • " . ($category->is_active ? 'Active' : 'Inactive'),
                'url' => route('admin.ecommerce.categories.edit', $category->id),
                'icon' => 'fas fa-folder',
                'color' => 'teal',
                'meta' => [
                    'slug' => $category->slug,
                    'products_count' => $category->products_count,
                    'status' => $category->is_active ? 'Active' : 'Inactive'
                ]
            ];
        })->toArray();
    }

    /**
     * Search Careers
     */
    private function searchCareers(string $query, int $limit): array
    {
        $careers = Career::where(function($q) use ($query) {
            $q->where('title', 'like', "%{$query}%")
              ->orWhere('department', 'like', "%{$query}%")
              ->orWhere('location', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->withCount('applications')
        ->limit($limit)
        ->get();

        return $careers->map(function($career) {
            return [
                'id' => $career->id,
                'type' => 'career',
                'title' => $career->title,
                'subtitle' => "{$career->department} • {$career->location}",
                'description' => "{$career->applications_count} applications • " . ($career->is_active ? 'Active' : 'Inactive'),
                'url' => route('admin.cms.careers.edit', $career->id),
                'icon' => 'fas fa-briefcase',
                'color' => 'indigo',
                'meta' => [
                    'department' => $career->department,
                    'location' => $career->location,
                    'applications_count' => $career->applications_count,
                    'status' => $career->is_active ? 'Active' : 'Inactive'
                ]
            ];
        })->toArray();
    }

    /**
     * Search Job Applications
     */
    private function searchJobApplications(string $query, int $limit): array
    {
        $applications = JobApplication::where(function($q) use ($query) {
            $q->where('reference_number', 'like', "%{$query}%")
              ->orWhere('first_name', 'like', "%{$query}%")
              ->orWhere('last_name', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%")
              ->orWhere('phone', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->with('career')
        ->limit($limit)
        ->get();

        return $applications->map(function($application) {
            return [
                'id' => $application->id,
                'type' => 'job_application',
                'title' => "Application #{$application->reference_number}",
                'subtitle' => $application->full_name,
                'description' => "Position: {$application->career?->title} • Status: {$application->status}",
                'url' => route('admin.cms.job-applications.show', $application->id),
                'icon' => 'fas fa-file-alt',
                'color' => 'pink',
                'meta' => [
                    'reference_number' => $application->reference_number,
                    'position' => $application->career?->title,
                    'status' => $application->status,
                    'email' => $application->email
                ]
            ];
        })->toArray();
    }

    /**
     * Search Blog Posts
     */
    private function searchBlogPosts(string $query, int $limit): array
    {
        $posts = BlogPost::where(function($q) use ($query) {
            $q->where('title', 'like', "%{$query}%")
              ->orWhere('slug', 'like', "%{$query}%")
              ->orWhere('content', 'like', "%{$query}%")
              ->orWhere('excerpt', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->with('author')
        ->limit($limit)
        ->get();

        return $posts->map(function($post) {
            return [
                'id' => $post->id,
                'type' => 'blog_post',
                'title' => $post->title,
                'subtitle' => "By {$post->author?->name}",
                'description' => "Status: {$post->status} • Views: {$post->view_count}",
                'url' => route('admin.blog.edit', $post->id),
                'icon' => 'fas fa-newspaper',
                'color' => 'cyan',
                'meta' => [
                    'slug' => $post->slug,
                    'status' => $post->status,
                    'author' => $post->author?->name,
                    'views' => $post->view_count
                ]
            ];
        })->toArray();
    }

    /**
     * Search Contacts
     */
    private function searchContacts(string $query, int $limit): array
    {
        $contacts = Contact::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('email', 'like', "%{$query}%")
              ->orWhere('phone', 'like', "%{$query}%")
              ->orWhere('subject', 'like', "%{$query}%")
              ->orWhere('message', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->limit($limit)
        ->get();

        return $contacts->map(function($contact) {
            return [
                'id' => $contact->id,
                'type' => 'contact',
                'title' => $contact->name,
                'subtitle' => $contact->email,
                'description' => "Subject: {$contact->subject} • Status: {$contact->status}",
                'url' => route('admin.communications.contacts.show', $contact->id),
                'icon' => 'fas fa-envelope',
                'color' => 'yellow',
                'meta' => [
                    'subject' => $contact->subject,
                    'status' => $contact->status,
                    'phone' => $contact->phone,
                    'created_at' => $contact->created_at->format('M d, Y')
                ]
            ];
        })->toArray();
    }

    /**
     * Search Newsletter Subscribers
     */
    private function searchNewsletterSubscribers(string $query, int $limit): array
    {
        $subscribers = NewsletterSubscriber::where(function($q) use ($query) {
            $q->where('email', 'like', "%{$query}%")
              ->orWhere('name', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->limit($limit)
        ->get();

        return $subscribers->map(function($subscriber) {
            return [
                'id' => $subscriber->id,
                'type' => 'newsletter_subscriber',
                'title' => $subscriber->name ?: $subscriber->email,
                'subtitle' => $subscriber->email,
                'description' => "Status: {$subscriber->status} • Source: {$subscriber->subscription_source}",
                'url' => route('admin.communications.newsletter.index') . '?search=' . urlencode($subscriber->email),
                'icon' => 'fas fa-mail-bulk',
                'color' => 'gray',
                'meta' => [
                    'status' => $subscriber->status,
                    'source' => $subscriber->subscription_source,
                    'subscribed_at' => $subscriber->subscribed_at?->format('M d, Y')
                ]
            ];
        })->toArray();
    }

    /**
     * Search Carriers
     */
    private function searchCarriers(string $query, int $limit): array
    {
        $carriers = Carrier::where(function($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('code', 'like', "%{$query}%")
              ->orWhere('contact_email', 'like', "%{$query}%")
              ->orWhere('contact_phone', 'like', "%{$query}%")
              ->orWhere('id', $query);
        })
        ->withCount('parcels')
        ->limit($limit)
        ->get();

        return $carriers->map(function($carrier) {
            return [
                'id' => $carrier->id,
                'type' => 'carrier',
                'title' => $carrier->name,
                'subtitle' => "Code: {$carrier->code}",
                'description' => "{$carrier->parcels_count} parcels • " . ($carrier->is_active ? 'Active' : 'Inactive'),
                'url' => route('admin.carriers.edit', $carrier->id),
                'icon' => 'fas fa-truck',
                'color' => 'red',
                'meta' => [
                    'code' => $carrier->code,
                    'type' => $carrier->type,
                    'parcels_count' => $carrier->parcels_count,
                    'status' => $carrier->is_active ? 'Active' : 'Inactive'
                ]
            ];
        })->toArray();
    }

    /**
     * Search Quote Requests
     */
    private function searchQuoteRequests(string $query, int $limit): array
    {
        // Note: QuoteRequest model needs to be properly implemented
        // For now, return empty array
        return [];
    }

    /**
     * Calculate relevance score for sorting
     */
    private function calculateRelevanceScore(array $result, string $query): int
    {
        $score = 0;
        $queryLower = strtolower($query);

        // Exact match in title gets highest score
        if (stripos($result['title'], $query) !== false) {
            $score += 100;
            if (stripos(strtolower($result['title']), $queryLower) === 0) {
                $score += 50; // Starts with query
            }
        }

        // Exact match in subtitle
        if (stripos($result['subtitle'], $query) !== false) {
            $score += 75;
        }

        // Match in description
        if (stripos($result['description'], $query) !== false) {
            $score += 25;
        }

        // Boost score for certain types
        switch ($result['type']) {
            case 'user':
            case 'order':
            case 'parcel':
                $score += 10; // These are often searched
                break;
        }

        return $score;
    }
}
