@extends('layouts.frontend')

@section('title', 'Unauthorized - 401')
@section('description', 'You need to be logged in to access this resource. Please login to continue.')

@section('content')
<!-- 401 Error Page -->
<section class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- 401 Animation -->
            <div class="mb-12">
                <div class="relative inline-block">
                    <!-- Large 401 Text -->
                    <h1 class="text-9xl md:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-800 leading-none select-none">
                        401
                    </h1>
                    
                    <!-- Floating Elements -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="relative">
                            <!-- User Icon -->
                            <div class="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center animate-bounce">
                                <i class="fas fa-user-slash text-4xl text-indigo-600"></i>
                            </div>
                            
                            <!-- Floating Particles -->
                            <div class="absolute -top-4 -right-4 w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-indigo-500 rounded-full animate-ping animation-delay-300"></div>
                            <div class="absolute top-1/2 -right-8 w-1.5 h-1.5 bg-purple-500 rounded-full animate-ping animation-delay-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Authentication Required
                </h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    You need to be logged in to access this resource. 
                    Please sign in to your account to continue.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <a href="{{ route('login') }}" 
                   class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-full hover:from-indigo-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-sign-in-alt mr-3"></i>
                    Login Now
                </a>
                
                <a href="{{ route('home') }}" 
                   class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-indigo-500 hover:text-indigo-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-home mr-3"></i>
                    Return Home
                </a>
            </div>

            <!-- Quick Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Login -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-sign-in-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Sign In</h3>
                    <p class="text-gray-600 mb-6">Access your account to continue</p>
                    <a href="{{ route('login') }}" 
                       class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                        Login <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Register -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-user-plus text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Create Account</h3>
                    <p class="text-gray-600 mb-6">Don't have an account? Sign up now</p>
                    <a href="{{ route('register') }}" 
                       class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                        Register <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Forgot Password -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-key text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Forgot Password?</h3>
                    <p class="text-gray-600 mb-6">Reset your password to regain access</p>
                    <a href="{{ route('password.request') }}" 
                       class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors">
                        Reset Password <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Why do I need to login?</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-shield-alt text-indigo-600 mr-3"></i>
                            Security & Privacy
                        </h4>
                        <p class="text-gray-600">
                            We protect your personal information and shipping data by requiring authentication 
                            for access to sensitive areas of our platform.
                        </p>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-user-cog text-indigo-600 mr-3"></i>
                            Personalized Experience
                        </h4>
                        <p class="text-gray-600">
                            Logging in allows us to provide you with personalized tracking information, 
                            order history, and customized shipping preferences.
                        </p>
                    </div>
                </div>
                
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h4 class="font-semibold text-gray-900 mb-4 text-center">Need Help?</h4>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('contact') }}" 
                           class="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Support
                        </a>
                        <span class="text-gray-400 hidden sm:block">|</span>
                        <a href="tel:{{ $siteSettings['contact_phone'] ?? '+****************' }}" 
                           class="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-colors">
                            <i class="fas fa-phone mr-2"></i>
                            {{ $siteSettings['contact_phone'] ?? '+****************' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .animation-delay-300 {
        animation-delay: 300ms;
    }
    
    .animation-delay-600 {
        animation-delay: 600ms;
    }
</style>

<script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.transform');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
@endsection
