@extends('layouts.frontend')

@section('title', 'Payment Required - 402')
@section('description', 'Payment is required to access this resource. Please complete your payment to continue.')

@section('content')
<!-- 402 Error Page -->
<section class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- 402 Animation -->
            <div class="mb-12">
                <div class="relative inline-block">
                    <!-- Large 402 Text -->
                    <h1 class="text-9xl md:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-green-600 via-emerald-600 to-teal-800 leading-none select-none">
                        402
                    </h1>
                    
                    <!-- Floating Elements -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="relative">
                            <!-- Credit Card Icon -->
                            <div class="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center animate-bounce">
                                <i class="fas fa-credit-card text-4xl text-emerald-600"></i>
                            </div>
                            
                            <!-- Floating Particles -->
                            <div class="absolute -top-4 -right-4 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-emerald-500 rounded-full animate-ping animation-delay-300"></div>
                            <div class="absolute top-1/2 -right-8 w-1.5 h-1.5 bg-teal-500 rounded-full animate-ping animation-delay-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Payment Required
                </h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    This resource requires payment to access. 
                    Please complete your payment or upgrade your account to continue.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                @auth
                    <a href="{{ route('customer.dashboard') }}" 
                       class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-full hover:from-emerald-700 hover:to-teal-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-credit-card mr-3"></i>
                        Make Payment
                    </a>
                @else
                    <a href="{{ route('login') }}" 
                       class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-full hover:from-emerald-700 hover:to-teal-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-sign-in-alt mr-3"></i>
                        Login to Pay
                    </a>
                @endauth
                
                <a href="{{ route('home') }}" 
                   class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-emerald-500 hover:text-emerald-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-home mr-3"></i>
                    Return Home
                </a>
            </div>

            <!-- Quick Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Payment Options -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-wallet text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Payment Methods</h3>
                    <p class="text-gray-600 mb-6">We accept various secure payment options</p>
                    <div class="flex justify-center space-x-2 mb-4">
                        <i class="fab fa-cc-visa text-2xl text-blue-600"></i>
                        <i class="fab fa-cc-mastercard text-2xl text-red-600"></i>
                        <i class="fab fa-cc-paypal text-2xl text-blue-500"></i>
                        <i class="fab fa-cc-stripe text-2xl text-purple-600"></i>
                    </div>
                    @auth
                        <a href="{{ route('customer.dashboard') }}" 
                           class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                            View Options <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    @else
                        <a href="{{ route('login') }}" 
                           class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                            Login First <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    @endauth
                </div>

                <!-- Account Status -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-user-check text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Account Status</h3>
                    <p class="text-gray-600 mb-6">Check your account and payment status</p>
                    @auth
                        <a href="{{ route('customer.dashboard') }}" 
                           class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-semibold transition-colors">
                            View Account <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    @else
                        <a href="{{ route('login') }}" 
                           class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-semibold transition-colors">
                            Login <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    @endauth
                </div>

                <!-- Support -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-headset text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Payment Support</h3>
                    <p class="text-gray-600 mb-6">Need help with payment issues?</p>
                    <a href="{{ route('contact') }}" 
                       class="inline-flex items-center text-teal-600 hover:text-teal-700 font-semibold transition-colors">
                        Get Help <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Secure Payment Processing</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt text-green-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Secure & Encrypted</h4>
                        <p class="text-gray-600 text-sm">
                            All payments are processed through secure, encrypted connections
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clock text-emerald-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Instant Processing</h4>
                        <p class="text-gray-600 text-sm">
                            Payments are processed instantly for immediate access
                        </p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-undo text-teal-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Refund Protection</h4>
                        <p class="text-gray-600 text-sm">
                            Eligible transactions are protected by our refund policy
                        </p>
                    </div>
                </div>
                
                <div class="pt-8 border-t border-gray-200">
                    <h4 class="font-semibold text-gray-900 mb-4 text-center">Payment Questions?</h4>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('contact') }}" 
                           class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-semibold transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            Email Support
                        </a>
                        <span class="text-gray-400 hidden sm:block">|</span>
                        <a href="tel:{{ $siteSettings['contact_phone'] ?? '+****************' }}" 
                           class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-semibold transition-colors">
                            <i class="fas fa-phone mr-2"></i>
                            {{ $siteSettings['contact_phone'] ?? '+****************' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .animation-delay-300 {
        animation-delay: 300ms;
    }
    
    .animation-delay-600 {
        animation-delay: 600ms;
    }
</style>

<script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.transform');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
@endsection
