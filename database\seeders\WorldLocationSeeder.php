<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class WorldLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedMoreCountries();
        $this->seedMoreAsianCountries();
        $this->seedAfricanCountries();
        $this->seedEuropeanCountries();
        $this->seedAmericanCountries();
        $this->seedOceaniaCountries();
    }

    private function seedMoreCountries(): void
    {
        $countries = [
            // More European Countries
            ['name' => 'Russia', 'code' => 'RUS', 'iso2' => 'RU', 'phone_code' => '+7', 'currency' => 'RUB', 'flag' => '🇷🇺', 'sort_order' => 88],
            ['name' => 'Ukraine', 'code' => 'UKR', 'iso2' => 'UA', 'phone_code' => '+380', 'currency' => 'UAH', 'flag' => '🇺🇦', 'sort_order' => 89],
            ['name' => 'Belarus', 'code' => 'BLR', 'iso2' => 'BY', 'phone_code' => '+375', 'currency' => 'BYN', 'flag' => '🇧🇾', 'sort_order' => 90],
            ['name' => 'Moldova', 'code' => 'MDA', 'iso2' => 'MD', 'phone_code' => '+373', 'currency' => 'MDL', 'flag' => '🇲🇩', 'sort_order' => 91],
            ['name' => 'Serbia', 'code' => 'SRB', 'iso2' => 'RS', 'phone_code' => '+381', 'currency' => 'RSD', 'flag' => '🇷🇸', 'sort_order' => 92],
            ['name' => 'Montenegro', 'code' => 'MNE', 'iso2' => 'ME', 'phone_code' => '+382', 'currency' => 'EUR', 'flag' => '🇲🇪', 'sort_order' => 93],
            ['name' => 'Bosnia and Herzegovina', 'code' => 'BIH', 'iso2' => 'BA', 'phone_code' => '+387', 'currency' => 'BAM', 'flag' => '🇧🇦', 'sort_order' => 94],
            ['name' => 'North Macedonia', 'code' => 'MKD', 'iso2' => 'MK', 'phone_code' => '+389', 'currency' => 'MKD', 'flag' => '🇲🇰', 'sort_order' => 95],
            ['name' => 'Albania', 'code' => 'ALB', 'iso2' => 'AL', 'phone_code' => '+355', 'currency' => 'ALL', 'flag' => '🇦🇱', 'sort_order' => 96],
            ['name' => 'Kosovo', 'code' => 'XKX', 'iso2' => 'XK', 'phone_code' => '+383', 'currency' => 'EUR', 'flag' => '🇽🇰', 'sort_order' => 97],

            // More Asian Countries
            ['name' => 'Afghanistan', 'code' => 'AFG', 'iso2' => 'AF', 'phone_code' => '+93', 'currency' => 'AFN', 'flag' => '🇦🇫', 'sort_order' => 98],
            ['name' => 'Kazakhstan', 'code' => 'KAZ', 'iso2' => 'KZ', 'phone_code' => '+7', 'currency' => 'KZT', 'flag' => '🇰🇿', 'sort_order' => 99],
            ['name' => 'Uzbekistan', 'code' => 'UZB', 'iso2' => 'UZ', 'phone_code' => '+998', 'currency' => 'UZS', 'flag' => '🇺🇿', 'sort_order' => 100],
            ['name' => 'Turkmenistan', 'code' => 'TKM', 'iso2' => 'TM', 'phone_code' => '+993', 'currency' => 'TMT', 'flag' => '🇹🇲', 'sort_order' => 101],
            ['name' => 'Kyrgyzstan', 'code' => 'KGZ', 'iso2' => 'KG', 'phone_code' => '+996', 'currency' => 'KGS', 'flag' => '🇰🇬', 'sort_order' => 102],
            ['name' => 'Tajikistan', 'code' => 'TJK', 'iso2' => 'TJ', 'phone_code' => '+992', 'currency' => 'TJS', 'flag' => '🇹🇯', 'sort_order' => 103],
            ['name' => 'Armenia', 'code' => 'ARM', 'iso2' => 'AM', 'phone_code' => '+374', 'currency' => 'AMD', 'flag' => '🇦🇲', 'sort_order' => 104],
            ['name' => 'Azerbaijan', 'code' => 'AZE', 'iso2' => 'AZ', 'phone_code' => '+994', 'currency' => 'AZN', 'flag' => '🇦🇿', 'sort_order' => 105],
            ['name' => 'Georgia', 'code' => 'GEO', 'iso2' => 'GE', 'phone_code' => '+995', 'currency' => 'GEL', 'flag' => '🇬🇪', 'sort_order' => 106],

            // Middle East
            ['name' => 'Iran', 'code' => 'IRN', 'iso2' => 'IR', 'phone_code' => '+98', 'currency' => 'IRR', 'flag' => '🇮🇷', 'sort_order' => 107],
            ['name' => 'Iraq', 'code' => 'IRQ', 'iso2' => 'IQ', 'phone_code' => '+964', 'currency' => 'IQD', 'flag' => '🇮🇶', 'sort_order' => 108],
            ['name' => 'Syria', 'code' => 'SYR', 'iso2' => 'SY', 'phone_code' => '+963', 'currency' => 'SYP', 'flag' => '🇸🇾', 'sort_order' => 109],
            ['name' => 'Yemen', 'code' => 'YEM', 'iso2' => 'YE', 'phone_code' => '+967', 'currency' => 'YER', 'flag' => '🇾🇪', 'sort_order' => 110],

            // More African Countries
            ['name' => 'Algeria', 'code' => 'DZA', 'iso2' => 'DZ', 'phone_code' => '+213', 'currency' => 'DZD', 'flag' => '🇩🇿', 'sort_order' => 111],
            ['name' => 'Tunisia', 'code' => 'TUN', 'iso2' => 'TN', 'phone_code' => '+216', 'currency' => 'TND', 'flag' => '🇹🇳', 'sort_order' => 112],
            ['name' => 'Libya', 'code' => 'LBY', 'iso2' => 'LY', 'phone_code' => '+218', 'currency' => 'LYD', 'flag' => '🇱🇾', 'sort_order' => 113],
            ['name' => 'Sudan', 'code' => 'SDN', 'iso2' => 'SD', 'phone_code' => '+249', 'currency' => 'SDG', 'flag' => '🇸🇩', 'sort_order' => 114],
            ['name' => 'Ethiopia', 'code' => 'ETH', 'iso2' => 'ET', 'phone_code' => '+251', 'currency' => 'ETB', 'flag' => '🇪🇹', 'sort_order' => 115],
            ['name' => 'Tanzania', 'code' => 'TZA', 'iso2' => 'TZ', 'phone_code' => '+255', 'currency' => 'TZS', 'flag' => '🇹🇿', 'sort_order' => 116],
            ['name' => 'Uganda', 'code' => 'UGA', 'iso2' => 'UG', 'phone_code' => '+256', 'currency' => 'UGX', 'flag' => '🇺🇬', 'sort_order' => 117],
            ['name' => 'Rwanda', 'code' => 'RWA', 'iso2' => 'RW', 'phone_code' => '+250', 'currency' => 'RWF', 'flag' => '🇷🇼', 'sort_order' => 118],
            ['name' => 'Botswana', 'code' => 'BWA', 'iso2' => 'BW', 'phone_code' => '+267', 'currency' => 'BWP', 'flag' => '🇧🇼', 'sort_order' => 119],
            ['name' => 'Namibia', 'code' => 'NAM', 'iso2' => 'NA', 'phone_code' => '+264', 'currency' => 'NAD', 'flag' => '🇳🇦', 'sort_order' => 120],
            ['name' => 'Zimbabwe', 'code' => 'ZWE', 'iso2' => 'ZW', 'phone_code' => '+263', 'currency' => 'ZWL', 'flag' => '🇿🇼', 'sort_order' => 121],
            ['name' => 'Zambia', 'code' => 'ZMB', 'iso2' => 'ZM', 'phone_code' => '+260', 'currency' => 'ZMW', 'flag' => '🇿🇲', 'sort_order' => 122],

            // Caribbean & Central America
            ['name' => 'Jamaica', 'code' => 'JAM', 'iso2' => 'JM', 'phone_code' => '+1876', 'currency' => 'JMD', 'flag' => '🇯🇲', 'sort_order' => 123],
            ['name' => 'Cuba', 'code' => 'CUB', 'iso2' => 'CU', 'phone_code' => '+53', 'currency' => 'CUP', 'flag' => '🇨🇺', 'sort_order' => 124],
            ['name' => 'Dominican Republic', 'code' => 'DOM', 'iso2' => 'DO', 'phone_code' => '+1809', 'currency' => 'DOP', 'flag' => '🇩🇴', 'sort_order' => 125],
            ['name' => 'Haiti', 'code' => 'HTI', 'iso2' => 'HT', 'phone_code' => '+509', 'currency' => 'HTG', 'flag' => '🇭🇹', 'sort_order' => 126],
            ['name' => 'Puerto Rico', 'code' => 'PRI', 'iso2' => 'PR', 'phone_code' => '+1787', 'currency' => 'USD', 'flag' => '🇵🇷', 'sort_order' => 127],
            ['name' => 'Trinidad and Tobago', 'code' => 'TTO', 'iso2' => 'TT', 'phone_code' => '+1868', 'currency' => 'TTD', 'flag' => '🇹🇹', 'sort_order' => 128],
            ['name' => 'Barbados', 'code' => 'BRB', 'iso2' => 'BB', 'phone_code' => '+1246', 'currency' => 'BBD', 'flag' => '🇧🇧', 'sort_order' => 129],
            ['name' => 'Costa Rica', 'code' => 'CRI', 'iso2' => 'CR', 'phone_code' => '+506', 'currency' => 'CRC', 'flag' => '🇨🇷', 'sort_order' => 130],
            ['name' => 'Panama', 'code' => 'PAN', 'iso2' => 'PA', 'phone_code' => '+507', 'currency' => 'PAB', 'flag' => '🇵🇦', 'sort_order' => 131],
            ['name' => 'Guatemala', 'code' => 'GTM', 'iso2' => 'GT', 'phone_code' => '+502', 'currency' => 'GTQ', 'flag' => '🇬🇹', 'sort_order' => 132],
            ['name' => 'Honduras', 'code' => 'HND', 'iso2' => 'HN', 'phone_code' => '+504', 'currency' => 'HNL', 'flag' => '🇭🇳', 'sort_order' => 133],
            ['name' => 'El Salvador', 'code' => 'SLV', 'iso2' => 'SV', 'phone_code' => '+503', 'currency' => 'USD', 'flag' => '🇸🇻', 'sort_order' => 134],
            ['name' => 'Nicaragua', 'code' => 'NIC', 'iso2' => 'NI', 'phone_code' => '+505', 'currency' => 'NIO', 'flag' => '🇳🇮', 'sort_order' => 135],
            ['name' => 'Belize', 'code' => 'BLZ', 'iso2' => 'BZ', 'phone_code' => '+501', 'currency' => 'BZD', 'flag' => '🇧🇿', 'sort_order' => 136],

            // More Pacific Countries
            ['name' => 'Samoa', 'code' => 'WSM', 'iso2' => 'WS', 'phone_code' => '+685', 'currency' => 'WST', 'flag' => '🇼🇸', 'sort_order' => 137],
            ['name' => 'Tonga', 'code' => 'TON', 'iso2' => 'TO', 'phone_code' => '+676', 'currency' => 'TOP', 'flag' => '🇹🇴', 'sort_order' => 138],
            ['name' => 'Vanuatu', 'code' => 'VUT', 'iso2' => 'VU', 'phone_code' => '+678', 'currency' => 'VUV', 'flag' => '🇻🇺', 'sort_order' => 139],
            ['name' => 'Solomon Islands', 'code' => 'SLB', 'iso2' => 'SB', 'phone_code' => '+677', 'currency' => 'SBD', 'flag' => '🇸🇧', 'sort_order' => 140],

            // Nordic Countries
            ['name' => 'Faroe Islands', 'code' => 'FRO', 'iso2' => 'FO', 'phone_code' => '+298', 'currency' => 'DKK', 'flag' => '🇫🇴', 'sort_order' => 141],
            ['name' => 'Greenland', 'code' => 'GRL', 'iso2' => 'GL', 'phone_code' => '+299', 'currency' => 'DKK', 'flag' => '🇬🇱', 'sort_order' => 142],

            // More Asian Countries
            ['name' => 'Macau', 'code' => 'MAC', 'iso2' => 'MO', 'phone_code' => '+853', 'currency' => 'MOP', 'flag' => '🇲🇴', 'sort_order' => 143],
            ['name' => 'North Korea', 'code' => 'PRK', 'iso2' => 'KP', 'phone_code' => '+850', 'currency' => 'KPW', 'flag' => '🇰🇵', 'sort_order' => 144],
        ];

        foreach ($countries as $countryData) {
            Country::firstOrCreate(
                ['iso2' => $countryData['iso2']],
                $countryData
            );
        }
    }

    private function seedMoreAsianCountries(): void
    {
        // Add major cities for some Asian countries without states
        $this->addCitiesToCountry('SG', ['Singapore', 'Jurong', 'Woodlands', 'Tampines', 'Sengkang', 'Hougang', 'Yishun', 'Bedok', 'Punggol', 'Ang Mo Kio']);
        $this->addCitiesToCountry('HK', ['Hong Kong', 'Kowloon', 'Tsuen Wan', 'Yuen Long', 'Tuen Mun', 'Tai Po', 'Sha Tin', 'Kwun Tong', 'Wong Tai Sin', 'Sham Shui Po']);
        $this->addCitiesToCountry('TW', ['Taipei', 'Kaohsiung', 'Taichung', 'Tainan', 'Taoyuan', 'Hsinchu', 'Keelung', 'Chiayi', 'Changhua', 'Yunlin']);
        $this->addCitiesToCountry('MY', ['Kuala Lumpur', 'George Town', 'Ipoh', 'Shah Alam', 'Petaling Jaya', 'Klang', 'Johor Bahru', 'Subang Jaya', 'Kuching', 'Kota Kinabalu']);
        $this->addCitiesToCountry('TH', ['Bangkok', 'Nonthaburi', 'Pak Kret', 'Hat Yai', 'Chiang Mai', 'Laem Chabang', 'Udon Thani', 'Nakhon Ratchasima', 'Khon Kaen', 'Ubon Ratchathani']);
        $this->addCitiesToCountry('ID', ['Jakarta', 'Surabaya', 'Bandung', 'Bekasi', 'Medan', 'Tangerang', 'Depok', 'Semarang', 'Palembang', 'Makassar']);
        $this->addCitiesToCountry('PH', ['Manila', 'Quezon City', 'Caloocan', 'Davao', 'Cebu City', 'Zamboanga', 'Antipolo', 'Pasig', 'Taguig', 'Valenzuela']);
        $this->addCitiesToCountry('VN', ['Ho Chi Minh City', 'Hanoi', 'Haiphong', 'Da Nang', 'Bien Hoa', 'Hue', 'Nha Trang', 'Can Tho', 'Rach Gia', 'Qui Nhon']);
    }

    private function seedAfricanCountries(): void
    {
        $this->addCitiesToCountry('ZA', ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth', 'Pietermaritzburg', 'Benoni', 'Tembisa', 'East London', 'Vereeniging']);
        $this->addCitiesToCountry('NG', ['Lagos', 'Kano', 'Ibadan', 'Abuja', 'Port Harcourt', 'Benin City', 'Maiduguri', 'Zaria', 'Aba', 'Jos']);
        $this->addCitiesToCountry('KE', ['Nairobi', 'Mombasa', 'Nakuru', 'Eldoret', 'Kisumu', 'Thika', 'Malindi', 'Kitale', 'Garissa', 'Kakamega']);
        $this->addCitiesToCountry('GH', ['Accra', 'Kumasi', 'Tamale', 'Takoradi', 'Tema', 'Cape Coast', 'Obuasi', 'Teshi Old Town', 'Madina', 'Koforidua']);
        $this->addCitiesToCountry('MA', ['Casablanca', 'Rabat', 'Fez', 'Marrakech', 'Agadir', 'Tangier', 'Meknes', 'Oujda', 'Kenitra', 'Tetouan']);
        $this->addCitiesToCountry('EG', ['Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said', 'Suez', 'Luxor', 'Mansoura', 'El Mahalla El Kubra', 'Tanta']);
    }

    private function seedEuropeanCountries(): void
    {
        $this->addCitiesToCountry('RU', ['Moscow', 'Saint Petersburg', 'Novosibirsk', 'Yekaterinburg', 'Nizhny Novgorod', 'Kazan', 'Chelyabinsk', 'Omsk', 'Samara', 'Rostov-on-Don']);
        $this->addCitiesToCountry('UA', ['Kyiv', 'Kharkiv', 'Odessa', 'Dnipro', 'Donetsk', 'Zaporizhzhia', 'Lviv', 'Kryvyi Rih', 'Mykolaiv', 'Mariupol']);
        $this->addCitiesToCountry('PL', ['Warsaw', 'Kraków', 'Łódź', 'Wrocław', 'Poznań', 'Gdańsk', 'Szczecin', 'Bydgoszcz', 'Lublin', 'Katowice']);
        $this->addCitiesToCountry('CZ', ['Prague', 'Brno', 'Ostrava', 'Plzen', 'Liberec', 'Olomouc', 'Budweis', 'Hradec Kralove', 'Usti nad Labem', 'Pardubice']);
        $this->addCitiesToCountry('HU', ['Budapest', 'Debrecen', 'Szeged', 'Miskolc', 'Pécs', 'Győr', 'Nyíregyháza', 'Kecskemét', 'Székesfehérvár', 'Szombathely']);
        $this->addCitiesToCountry('RO', ['Bucharest', 'Cluj-Napoca', 'Timișoara', 'Iași', 'Constanța', 'Craiova', 'Brașov', 'Galați', 'Ploiești', 'Oradea']);
    }

    private function seedAmericanCountries(): void
    {
        $this->addCitiesToCountry('AR', ['Buenos Aires', 'Córdoba', 'Rosario', 'Mendoza', 'Tucumán', 'La Plata', 'Mar del Plata', 'Salta', 'Santa Fe', 'San Juan']);
        $this->addCitiesToCountry('CL', ['Santiago', 'Valparaíso', 'Concepción', 'La Serena', 'Antofagasta', 'Temuco', 'Rancagua', 'Talca', 'Arica', 'Chillán']);
        $this->addCitiesToCountry('CO', ['Bogotá', 'Medellín', 'Cali', 'Barranquilla', 'Cartagena', 'Cúcuta', 'Soledad', 'Ibagué', 'Bucaramanga', 'Soacha']);
        $this->addCitiesToCountry('PE', ['Lima', 'Arequipa', 'Trujillo', 'Chiclayo', 'Huancayo', 'Piura', 'Iquitos', 'Cusco', 'Chimbote', 'Tacna']);
        $this->addCitiesToCountry('VE', ['Caracas', 'Maracaibo', 'Valencia', 'Barquisimeto', 'Maracay', 'Ciudad Guayana', 'San Cristóbal', 'Maturín', 'Ciudad Bolívar', 'Cumana']);
    }

    private function seedOceaniaCountries(): void
    {
        $this->addCitiesToCountry('NZ', ['Auckland', 'Wellington', 'Christchurch', 'Hamilton', 'Tauranga', 'Napier-Hastings', 'Dunedin', 'Palmerston North', 'Nelson', 'Rotorua']);
        $this->addCitiesToCountry('FJ', ['Suva', 'Nadi', 'Lautoka', 'Labasa', 'Ba', 'Tavua', 'Levuka', 'Sigatoka', 'Nausori', 'Savusavu']);
        $this->addCitiesToCountry('PG', ['Port Moresby', 'Lae', 'Mount Hagen', 'Popondetta', 'Madang', 'Wewak', 'Vanimo', 'Kerema', 'Daru', 'Mendi']);
    }

    private function addCitiesToCountry(string $iso2, array $cities): void
    {
        $country = Country::where('iso2', $iso2)->first();
        if (!$country) return;

        // Create a default state for countries without states
        $state = State::firstOrCreate(
            [
                'country_id' => $country->id,
                'name' => $country->name,
                'code' => 'DEFAULT'
            ],
            [
                'country_id' => $country->id,
                'name' => $country->name,
                'code' => 'DEFAULT',
                'is_active' => true,
                'sort_order' => 1
            ]
        );

        foreach ($cities as $index => $cityName) {
            City::firstOrCreate(
                [
                    'state_id' => $state->id,
                    'name' => $cityName
                ],
                [
                    'state_id' => $state->id,
                    'name' => $cityName,
                    'is_active' => true,
                    'sort_order' => $index + 1
                ]
            );
        }
    }
}
