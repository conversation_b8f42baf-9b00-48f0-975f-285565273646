<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'location',
    'countryValue' => '',
    'stateValue' => '',
    'cityValue' => '',
    'required' => false,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'style' => 'tailwind', // bootstrap or tailwind
    'disabled' => false,
    'readonly' => false,
    'containerClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => ''
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'location',
    'countryValue' => '',
    'stateValue' => '',
    'cityValue' => '',
    'required' => false,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'style' => 'tailwind', // bootstrap or tailwind
    'disabled' => false,
    'readonly' => false,
    'containerClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => ''
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $baseClasses = $style === 'tailwind' 
        ? 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
        : 'form-control';
    
    $labelClasses = $style === 'tailwind'
        ? 'block text-sm font-medium text-gray-700 mb-2'
        : 'form-label';
        
    $containerClasses = $style === 'tailwind'
        ? 'grid md:grid-cols-3 gap-6'
        : 'row';
        
    $colClasses = $style === 'tailwind'
        ? ''
        : 'col-md-4 mb-3';
        
    // Apply custom classes
    if ($containerClass) {
        $containerClasses = $containerClass;
    }
    if ($inputClass) {
        $baseClasses = $inputClass;
    }
    if ($labelClass) {
        $labelClasses = $labelClass;
    }
?>

<div class="<?php echo e($containerClasses); ?>" data-location-container="<?php echo e($name); ?>">
    <?php if($showCountry): ?>
    <div class="<?php echo e($colClasses); ?>">
        <label for="<?php echo e($name); ?>_country" class="<?php echo e($labelClasses); ?>">
            Country <?php echo e($required ? '*' : ''); ?>

        </label>
        <select id="<?php echo e($name); ?>_country" 
                name="country" 
                class="<?php echo e($baseClasses); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                <?php echo e($disabled ? 'disabled' : ''); ?>

                <?php echo e($readonly ? 'readonly' : ''); ?>

                data-location-type="country"
                data-location-name="<?php echo e($name); ?>">
            <option value="">Select Country</option>
        </select>
        <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="<?php echo e($errorClass ?: ($style === 'tailwind' ? 'text-red-500 text-sm mt-1' : 'text-danger small')); ?>"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>
    
    <?php if($showState): ?>
    <div class="<?php echo e($colClasses); ?>">
        <label for="<?php echo e($name); ?>_state" class="<?php echo e($labelClasses); ?>">
            State/Province <?php echo e($required ? '*' : ''); ?>

        </label>
        <select id="<?php echo e($name); ?>_state" 
                name="state" 
                class="<?php echo e($baseClasses); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                <?php echo e($disabled ? 'disabled' : ''); ?>

                <?php echo e($readonly ? 'readonly' : ''); ?>

                data-location-type="state"
                data-location-name="<?php echo e($name); ?>"
                disabled>
            <option value="">Select State/Province</option>
        </select>
        <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="<?php echo e($errorClass ?: ($style === 'tailwind' ? 'text-red-500 text-sm mt-1' : 'text-danger small')); ?>"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>
    
    <?php if($showCity): ?>
    <div class="<?php echo e($colClasses); ?>">
        <label for="<?php echo e($name); ?>_city" class="<?php echo e($labelClasses); ?>">
            City <?php echo e($required ? '*' : ''); ?>

        </label>
        <select id="<?php echo e($name); ?>_city" 
                name="city" 
                class="<?php echo e($baseClasses); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                <?php echo e($disabled ? 'disabled' : ''); ?>

                <?php echo e($readonly ? 'readonly' : ''); ?>

                data-location-type="city"
                data-location-name="<?php echo e($name); ?>"
                disabled>
            <option value="">Select City</option>
        </select>
        <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <p class="<?php echo e($errorClass ?: ($style === 'tailwind' ? 'text-red-500 text-sm mt-1' : 'text-danger small')); ?>"><?php echo e($message); ?></p>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('81ac8e43-ace3-466d-b9d2-79adbf812975')): $__env->markAsRenderedOnce('81ac8e43-ace3-466d-b9d2-79adbf812975'); ?>
<?php $__env->startPush('styles'); ?>
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/css/tom-select.css" rel="stylesheet">
<style>
    .ts-control {
        border: 1px solid #d1d5db !important;
        border-radius: 0.375rem !important;
        min-height: 42px !important;
    }
    
    .ts-control.focus {
        border-color: #10b981 !important;
        box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
    }
    
    .ts-dropdown {
        border: 1px solid #d1d5db !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }
    
    .ts-dropdown .option {
        padding: 8px 12px !important;
    }
    
    .ts-dropdown .option.active {
        background-color: #10b981 !important;
        color: white !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/js/tom-select.complete.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeLocationDropdown('<?php echo e($name); ?>', {
        countryValue: '<?php echo e($countryValue); ?>',
        stateValue: '<?php echo e($stateValue); ?>',
        cityValue: '<?php echo e($cityValue); ?>'
    });
});

function initializeLocationDropdown(name, initialValues = {}) {
    const container = document.querySelector(`[data-location-container="${name}"]`);
    if (!container) return;
    
    const countrySelect = container.querySelector(`#${name}_country`);
    const stateSelect = container.querySelector(`#${name}_state`);
    const citySelect = container.querySelector(`#${name}_city`);
    
    let countryTomSelect, stateTomSelect, cityTomSelect;
    
    // Initialize country dropdown
    if (countrySelect && typeof TomSelect !== 'undefined') {
        // Check if already initialized
        if (countrySelect.tomselect) {
            countryTomSelect = countrySelect.tomselect;
        } else {
            try {
                countryTomSelect = new TomSelect(countrySelect, {
                    placeholder: 'Type to search countries...',
                    searchField: ['text'],
                    valueField: 'value',
                    labelField: 'text',
                    preload: true,
                    load: function(query, callback) {
                        fetch('/api/locations/countries')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    const options = data.data.map(country => ({
                                        value: country.name,
                                        text: country.name,
                                        countryId: country.id
                                    }));
                                    callback(options);
                                } else {
                                    callback();
                                }
                            })
                            .catch(() => callback());
                    },
                    onChange: function(value) {
                        // Reset and disable dependent dropdowns
                        if (stateTomSelect) {
                            stateTomSelect.clear();
                            stateTomSelect.clearOptions();
                            stateTomSelect.disable();
                        }
                        if (cityTomSelect) {
                            cityTomSelect.clear();
                            cityTomSelect.clearOptions();
                            cityTomSelect.disable();
                        }

                        if (value && stateSelect) {
                            // Get the country ID from the selected option
                            const selectedOption = countryTomSelect.options[value];
                            if (selectedOption && selectedOption.countryId) {
                                loadStates(selectedOption.countryId);
                            }
                        }
                    }
                });

                // Load initial countries
                countryTomSelect.load('');
            } catch (error) {
                console.error('Error initializing country TomSelect:', error);
            }
        }
    }
    
    // Initialize state dropdown
    if (stateSelect && typeof TomSelect !== 'undefined') {
        if (stateSelect.tomselect) {
            stateTomSelect = stateSelect.tomselect;
        } else {
            try {
                stateTomSelect = new TomSelect(stateSelect, {
                    placeholder: 'Type to search states/provinces...',
                    searchField: ['text'],
                    valueField: 'value',
                    labelField: 'text',
                    onChange: function(value) {
                        // Reset and disable city dropdown
                        if (cityTomSelect) {
                            cityTomSelect.clear();
                            cityTomSelect.clearOptions();
                            cityTomSelect.disable();
                        }

                        if (value && citySelect) {
                            // Get the state ID from the selected option
                            const selectedOption = stateTomSelect.options[value];
                            if (selectedOption && selectedOption.stateId) {
                                loadCities(selectedOption.stateId);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error initializing state TomSelect:', error);
            }
        }
    }
    
    // Initialize city dropdown
    if (citySelect && typeof TomSelect !== 'undefined') {
        if (citySelect.tomselect) {
            cityTomSelect = citySelect.tomselect;
        } else {
            try {
                cityTomSelect = new TomSelect(citySelect, {
                    placeholder: 'Type to search cities...',
                    searchField: ['text'],
                    valueField: 'value',
                    labelField: 'text',
                    onChange: function(value) {
                        // Auto-complete country and state when city is selected
                        if (value) {
                            const selectedOption = cityTomSelect.options[value];
                            if (selectedOption && selectedOption.countryName && selectedOption.stateName) {
                                // Set country if not already set
                                if (countryTomSelect && !countryTomSelect.getValue()) {
                                    countryTomSelect.setValue(selectedOption.countryName);
                                }
                                // Set state if not already set
                                if (stateTomSelect && !stateTomSelect.getValue()) {
                                    stateTomSelect.setValue(selectedOption.stateName);
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error initializing city TomSelect:', error);
            }
        }
    }
    
    function loadStates(countryId, initialStateValue = null) {
        if (!stateTomSelect) return;
        
        fetch(`/api/locations/states/${countryId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stateTomSelect.clearOptions();
                    const options = data.data.map(state => ({
                        value: state.name,
                        text: state.name,
                        stateId: state.id
                    }));
                    
                    stateTomSelect.addOptions(options);
                    stateTomSelect.enable();
                    
                    // Set initial value if provided
                    if (initialStateValue) {
                        stateTomSelect.setValue(initialStateValue);
                    }
                }
            })
            .catch(error => {
                console.error('Error loading states:', error);
            });
    }
    
    function loadCities(stateId, initialCityValue = null) {
        if (!cityTomSelect) return;
        
        fetch(`/api/locations/cities/${stateId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cityTomSelect.clearOptions();
                    const options = data.data.map(city => ({
                        value: city.name,
                        text: city.name,
                        cityId: city.id,
                        countryName: city.country_name,
                        stateName: city.state_name
                    }));
                    
                    cityTomSelect.addOptions(options);
                    cityTomSelect.enable();
                    
                    // Set initial value if provided
                    if (initialCityValue) {
                        cityTomSelect.setValue(initialCityValue);
                    }
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
            });
    }
    
    // Set initial values if provided
    if (initialValues.countryValue && countryTomSelect) {
        setTimeout(() => {
            countryTomSelect.setValue(initialValues.countryValue);
        }, 500);
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/components/location-dropdown.blade.php ENDPATH**/ ?>