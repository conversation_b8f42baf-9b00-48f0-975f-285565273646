<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class ProfileController extends Controller
{
    /**
     * Show customer profile
     */
    public function show(): View
    {
        $user = Auth::user();
        return view('customer.profile.show', compact('user'));
    }

    /**
     * Show edit profile form
     */
    public function edit(): View
    {
        $user = Auth::user();
        return view('customer.profile.edit', compact('user'));
    }

    /**
     * Update customer profile
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'location.country' => 'nullable|integer|exists:countries,id',
            'location.state' => 'nullable|integer|exists:states,id',
            'location.city' => 'nullable|integer|exists:cities,id',
            'postal_code' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
        ]);

        // Get location names from IDs
        $countryName = null;
        $stateName = null;
        $cityName = null;

        if (!empty($validated['location']['country'])) {
            $country = \App\Models\Country::find($validated['location']['country']);
            $countryName = $country ? $country->name : null;
        }

        if (!empty($validated['location']['state'])) {
            $state = \App\Models\State::find($validated['location']['state']);
            $stateName = $state ? $state->name : null;
        }

        if (!empty($validated['location']['city'])) {
            $city = \App\Models\City::find($validated['location']['city']);
            $cityName = $city ? $city->name : null;
        }

        // Update user with location names
        $user->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'address' => $validated['address'],
            'city' => $cityName,
            'state' => $stateName,
            'country' => $countryName,
            'postal_code' => $validated['postal_code'],
            'company_name' => $validated['company_name'],
        ]);

        return redirect()->route('customer.profile.show')
                        ->with('success', 'Profile updated successfully.');
    }

    /**
     * Show change password form
     */
    public function showChangePassword(): View
    {
        return view('customer.profile.change-password');
    }

    /**
     * Update customer password
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        Auth::user()->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('customer.profile.show')
                        ->with('success', 'Password updated successfully.');
    }

    /**
     * Show preferences form
     */
    public function showPreferences(): View
    {
        $user = Auth::user();
        return view('customer.profile.preferences', compact('user'));
    }

    /**
     * Update customer preferences
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        $user = Auth::user();

        // Validate all preference fields dynamically
        $validated = $request->validate([
            'preferences.email_order_updates' => 'boolean',
            'preferences.email_shipping_updates' => 'boolean',
            'preferences.email_delivery_updates' => 'boolean',
            'preferences.email_quote_updates' => 'boolean',
            'preferences.email_marketing' => 'boolean',
            'preferences.sms_order_updates' => 'boolean',
            'preferences.sms_shipping_updates' => 'boolean',
            'preferences.sms_delivery_updates' => 'boolean',
            'preferences.date_format' => 'string|in:MM/DD/YYYY,DD/MM/YYYY,YYYY-MM-DD',
            'preferences.time_format' => 'string|in:12,24',
            'preferences.currency' => 'string|in:USD,EUR,GBP,CAD,AUD',
            'preferences.weight_unit' => 'string|in:kg,lb',
            'preferences.distance_unit' => 'string|in:km,mi',
            'preferences.preferred_contact' => 'string|in:email,phone,sms',
            'preferences.contact_frequency' => 'string|in:as_needed,daily,weekly',
        ]);

        // Get current preferences or empty array
        $currentPreferences = $user->preferences ?? [];

        // Merge with new preferences, ensuring booleans are properly handled
        $newPreferences = array_merge($currentPreferences, [
            'email_order_updates' => $request->boolean('preferences.email_order_updates'),
            'email_shipping_updates' => $request->boolean('preferences.email_shipping_updates'),
            'email_delivery_updates' => $request->boolean('preferences.email_delivery_updates'),
            'email_quote_updates' => $request->boolean('preferences.email_quote_updates'),
            'email_marketing' => $request->boolean('preferences.email_marketing'),
            'sms_order_updates' => $request->boolean('preferences.sms_order_updates'),
            'sms_shipping_updates' => $request->boolean('preferences.sms_shipping_updates'),
            'sms_delivery_updates' => $request->boolean('preferences.sms_delivery_updates'),
            'date_format' => $validated['preferences']['date_format'] ?? 'MM/DD/YYYY',
            'time_format' => $validated['preferences']['time_format'] ?? '12',
            'currency' => $validated['preferences']['currency'] ?? 'USD',
            'weight_unit' => $validated['preferences']['weight_unit'] ?? 'kg',
            'distance_unit' => $validated['preferences']['distance_unit'] ?? 'km',
            'preferred_contact' => $validated['preferences']['preferred_contact'] ?? 'email',
            'contact_frequency' => $validated['preferences']['contact_frequency'] ?? 'as_needed',
        ]);

        $user->update(['preferences' => $newPreferences]);

        return redirect()->route('customer.profile.preferences')
                        ->with('success', 'Preferences updated successfully.');
    }

    /**
     * Show address book
     */
    public function addressBook(): View
    {
        $user = Auth::user();
        // For now, we'll show the user's main address
        // In a full implementation, you might have a separate addresses table
        return view('customer.profile.address-book', compact('user'));
    }

    /**
     * Delete customer account
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validate([
            'password' => 'required|current_password',
        ]);

        $user = Auth::user();

        // In a real application, you might want to:
        // 1. Anonymize the user data instead of deleting
        // 2. Keep order history for business records
        // 3. Send a confirmation email

        Auth::logout();

        // For now, we'll just deactivate the account
        $user->update(['is_active' => false]);

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('customer.login')
                        ->with('success', 'Your account has been deactivated successfully.');
    }
}
