<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => 'address',
    'label' => 'Address',
    'required' => false,
    'value' => null,
    'showPersonalInfo' => true,
    'showCompany' => true,
    'showPhone' => true,
    'showAddressType' => false,
    'addressTypes' => ['shipping' => 'Shipping', 'billing' => 'Billing', 'both' => 'Both'],
    'style' => 'tailwind', // 'tailwind' or 'bootstrap'
    'containerClass' => '',
    'gridClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => '',
    'disabled' => false,
    'readonly' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => 'address',
    'label' => 'Address',
    'required' => false,
    'value' => null,
    'showPersonalInfo' => true,
    'showCompany' => true,
    'showPhone' => true,
    'showAddressType' => false,
    'addressTypes' => ['shipping' => 'Shipping', 'billing' => 'Billing', 'both' => 'Both'],
    'style' => 'tailwind', // 'tailwind' or 'bootstrap'
    'containerClass' => '',
    'gridClass' => '',
    'inputClass' => '',
    'labelClass' => '',
    'errorClass' => '',
    'disabled' => false,
    'readonly' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $baseId = $name;
    
    // Determine CSS classes based on style
    $inputBaseClass = $style === 'bootstrap' 
        ? 'form-control' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $selectBaseClass = $style === 'bootstrap' 
        ? 'form-select' 
        : 'w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-transparent';
    
    $labelBaseClass = $style === 'bootstrap'
        ? 'form-label'
        : 'block text-sm font-medium text-gray-700 mb-2';
        
    $containerBaseClass = $style === 'bootstrap'
        ? 'mb-3'
        : 'mb-4';
        
    $gridBaseClass = $style === 'bootstrap'
        ? 'row'
        : 'grid grid-cols-1 md:grid-cols-2 gap-4';
        
    $colClass = $style === 'bootstrap'
        ? 'col-md-6'
        : '';

    // Get values from old input or passed value
    $addressData = old($name, $value ?? []);
?>

<div class="address-form-container <?php echo e($containerClass); ?>" data-name="<?php echo e($name); ?>">
    <?php if($showAddressType): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_type" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Address Type
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <select 
            name="<?php echo e($name); ?>[type]" 
            id="<?php echo e($baseId); ?>_type"
            class="<?php echo e($selectBaseClass); ?> <?php echo e($inputClass); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
            <option value="">Select Type</option>
            <?php $__currentLoopData = $addressTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typeValue => $typeLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($typeValue); ?>" <?php echo e((($addressData['type'] ?? '') === $typeValue) ? 'selected' : ''); ?>>
                    <?php echo e($typeLabel); ?>

                </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
        <?php $__errorArgs = [$name . '.type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>

    <?php if($showPersonalInfo): ?>
    <div class="<?php echo e($gridBaseClass); ?> <?php echo e($gridClass); ?>">
        <div class="<?php echo e($colClass); ?> <?php echo e($containerBaseClass); ?>">
            <label for="<?php echo e($baseId); ?>_first_name" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
                First Name
                <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
            </label>
            <input 
                type="text" 
                name="<?php echo e($name); ?>[first_name]" 
                id="<?php echo e($baseId); ?>_first_name"
                class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
                value="<?php echo e($addressData['first_name'] ?? ''); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                <?php echo e($disabled ? 'disabled' : ''); ?>

                <?php echo e($readonly ? 'readonly' : ''); ?>

            >
            <?php $__errorArgs = [$name . '.first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
        
        <div class="<?php echo e($colClass); ?> <?php echo e($containerBaseClass); ?>">
            <label for="<?php echo e($baseId); ?>_last_name" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
                Last Name
                <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
            </label>
            <input 
                type="text" 
                name="<?php echo e($name); ?>[last_name]" 
                id="<?php echo e($baseId); ?>_last_name"
                class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
                value="<?php echo e($addressData['last_name'] ?? ''); ?>"
                <?php echo e($required ? 'required' : ''); ?>

                <?php echo e($disabled ? 'disabled' : ''); ?>

                <?php echo e($readonly ? 'readonly' : ''); ?>

            >
            <?php $__errorArgs = [$name . '.last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if($showCompany): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_company" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Company (Optional)
        </label>
        <input 
            type="text" 
            name="<?php echo e($name); ?>[company]" 
            id="<?php echo e($baseId); ?>_company"
            class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
            value="<?php echo e($addressData['company'] ?? ''); ?>"
            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
        <?php $__errorArgs = [$name . '.company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>

    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_address_line_1" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Address Line 1
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <input 
            type="text" 
            name="<?php echo e($name); ?>[address_line_1]" 
            id="<?php echo e($baseId); ?>_address_line_1"
            class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
            value="<?php echo e($addressData['address_line_1'] ?? ''); ?>"
            placeholder="Street address, P.O. box, company name, c/o"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
        <?php $__errorArgs = [$name . '.address_line_1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_address_line_2" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Address Line 2 (Optional)
        </label>
        <input 
            type="text" 
            name="<?php echo e($name); ?>[address_line_2]" 
            id="<?php echo e($baseId); ?>_address_line_2"
            class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
            value="<?php echo e($addressData['address_line_2'] ?? ''); ?>"
            placeholder="Apartment, suite, unit, building, floor, etc."
            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
        <?php $__errorArgs = [$name . '.address_line_2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <!-- Location Dropdown Component -->
    <?php if (isset($component)) { $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.location-dropdown','data' => ['name' => $name,'required' => $required,'style' => $style,'countryValue' => $addressData['country'] ?? '','stateValue' => $addressData['state'] ?? '','cityValue' => $addressData['city'] ?? '','disabled' => $disabled,'readonly' => $readonly,'containerClass' => $containerClass,'inputClass' => $inputClass,'labelClass' => $labelClass,'errorClass' => $errorClass]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('location-dropdown'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($name),'required' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($required),'style' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($style),'country-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($addressData['country'] ?? ''),'state-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($addressData['state'] ?? ''),'city-value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($addressData['city'] ?? ''),'disabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($disabled),'readonly' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($readonly),'container-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($containerClass),'input-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($inputClass),'label-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($labelClass),'error-class' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errorClass)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $attributes = $__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__attributesOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5)): ?>
<?php $component = $__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5; ?>
<?php unset($__componentOriginalcc8c3fc7ab5db35b0ce5203eb007f5f5); ?>
<?php endif; ?>

    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_postal_code" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Postal Code
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <input 
            type="text" 
            name="<?php echo e($name); ?>[postal_code]" 
            id="<?php echo e($baseId); ?>_postal_code"
            class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
            value="<?php echo e($addressData['postal_code'] ?? ''); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
        <?php $__errorArgs = [$name . '.postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <?php if($showPhone): ?>
    <div class="<?php echo e($containerBaseClass); ?>">
        <label for="<?php echo e($baseId); ?>_phone" class="<?php echo e($labelBaseClass); ?> <?php echo e($labelClass); ?>">
            Phone Number
            <?php if($required): ?><span class="text-red-500">*</span><?php endif; ?>
        </label>
        <input 
            type="tel" 
            name="<?php echo e($name); ?>[phone]" 
            id="<?php echo e($baseId); ?>_phone"
            class="<?php echo e($inputBaseClass); ?> <?php echo e($inputClass); ?>"
            value="<?php echo e($addressData['phone'] ?? ''); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($readonly ? 'readonly' : ''); ?>

        >
        <?php $__errorArgs = [$name . '.phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="text-red-500 text-sm mt-1"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addressForm = document.querySelector('[data-name="<?php echo e($name); ?>"]');
    if (addressForm) {
        // Auto-format postal code based on country
        const postalCodeInput = addressForm.querySelector('#<?php echo e($baseId); ?>_postal_code');
        const countrySelect = addressForm.querySelector('#<?php echo e($name); ?>_country');
        
        if (postalCodeInput && countrySelect) {
            function formatPostalCode() {
                const countryOption = countrySelect.options[countrySelect.selectedIndex];
                const countryCode = countryOption ? countryOption.getAttribute('data-iso2') : '';
                let value = postalCodeInput.value.toUpperCase();
                
                // US ZIP code format
                if (countryCode === 'US' && /^\d{5}$/.test(value)) {
                    postalCodeInput.value = value;
                }
                // Canadian postal code format
                else if (countryCode === 'CA' && /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(value.replace(/\s/g, ''))) {
                    value = value.replace(/\s/g, '');
                    postalCodeInput.value = value.replace(/([A-Z]\d[A-Z])(\d[A-Z]\d)/, '$1 $2');
                }
                // UK postal code format
                else if (countryCode === 'GB') {
                    // Basic UK postcode formatting
                    postalCodeInput.value = value;
                }
            }
            
            postalCodeInput.addEventListener('input', formatPostalCode);
            countrySelect.addEventListener('change', formatPostalCode);
        }
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/components/address-form.blade.php ENDPATH**/ ?>