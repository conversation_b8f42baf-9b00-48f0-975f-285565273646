<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Parcel extends Model
{
    protected $fillable = [
        'tracking_number',
        'carrier_id',
        'user_id',
        'sender_name',
        'sender_email',
        'sender_phone',
        'sender_address',
        'sender_city',
        'sender_state',
        'sender_postal_code',
        'sender_country',
        'recipient_name',
        'recipient_email',
        'recipient_phone',
        'recipient_address',
        'recipient_city',
        'recipient_state',
        'recipient_postal_code',
        'recipient_country',
        'description',
        'weight',
        'dimensions',
        'declared_value',
        'service_type',
        'status',
        'special_instructions',
        'shipped_at',
        'delivered_at',
        'estimated_delivery_date',
        'shipping_cost',
        'insurance_cost',
        'total_cost',
        'is_paid',
        'requires_insurance',
        'insurance_paid',
        'insurance_refund_amount',
        'insurance_refunded_at',
        'insurance_notes',
        'has_diplomatic_envelope',
        'diplomatic_envelope_cost',
        'diplomatic_envelope_details',
        'special_services',
        'customer_alerts',
        'customer_notified_insurance',
        'customer_notified_diplomatic',
        'requires_admin_approval',
        'is_approved',
        'approved_by',
        'approved_at',
        'approval_notes',
    ];

    protected $casts = [
        'weight' => 'decimal:2',
        'declared_value' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'insurance_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'is_paid' => 'boolean',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'estimated_delivery_date' => 'date',
        'requires_insurance' => 'boolean',
        'insurance_paid' => 'boolean',
        'insurance_refund_amount' => 'decimal:2',
        'insurance_refunded_at' => 'datetime',
        'has_diplomatic_envelope' => 'boolean',
        'diplomatic_envelope_cost' => 'decimal:2',
        'special_services' => 'array',
        'customer_alerts' => 'array',
        'customer_notified_insurance' => 'boolean',
        'customer_notified_diplomatic' => 'boolean',
        'requires_admin_approval' => 'boolean',
        'is_approved' => 'boolean',
        'approved_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($parcel) {
            if (empty($parcel->tracking_number)) {
                $parcel->tracking_number = static::generateTrackingNumber();
            }
        });
    }

    /**
     * Generate a unique tracking number
     */
    public static function generateTrackingNumber(): string
    {
        $prefix = config('app.tracking_prefix', 'ATX');
        $year = date('Y');

        do {
            $sequence = str_pad(mt_rand(1, 99999999), 8, '0', STR_PAD_LEFT);
            $trackingNumber = "{$prefix}-{$year}-{$sequence}";
        } while (static::where('tracking_number', $trackingNumber)->exists());

        return $trackingNumber;
    }

    /**
     * Get the carrier for this parcel
     */
    public function carrier(): BelongsTo
    {
        return $this->belongsTo(Carrier::class);
    }

    /**
     * Get the user who created this parcel
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all tracking events for this parcel
     */
    public function trackingEvents(): HasMany
    {
        return $this->hasMany(TrackingEvent::class)->orderBy('event_date', 'desc');
    }

    /**
     * Get public tracking events only
     */
    public function publicTrackingEvents(): HasMany
    {
        return $this->hasMany(TrackingEvent::class)
                    ->where('is_public', true)
                    ->orderBy('event_date', 'desc');
    }

    /**
     * Get the payments for this parcel
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the user who approved this parcel
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope to filter by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get delivered parcels
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope to get parcels in transit
     */
    public function scopeInTransit($query)
    {
        return $query->whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery']);
    }

    /**
     * Check if parcel is delivered
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColor(): string
    {
        return match($this->status) {
            'pending' => 'secondary',
            'picked_up' => 'info',
            'in_transit' => 'primary',
            'out_for_delivery' => 'warning',
            'delivered' => 'success',
            'exception' => 'danger',
            'returned' => 'dark',
            default => 'secondary',
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus(): string
    {
        return Str::title(str_replace('_', ' ', $this->status));
    }

    /**
     * Check if parcel requires insurance payment
     */
    public function requiresInsurancePayment(): bool
    {
        return $this->requires_insurance && !$this->insurance_paid;
    }

    /**
     * Check if insurance is refundable
     */
    public function isInsuranceRefundable(): bool
    {
        return $this->requires_insurance && $this->insurance_paid && !$this->insurance_refunded_at;
    }

    /**
     * Get insurance status message
     */
    public function getInsuranceStatusMessage(): string
    {
        if (!$this->requires_insurance) {
            return 'No insurance required';
        }

        if (!$this->insurance_paid) {
            return 'Insurance payment required before delivery';
        }

        if ($this->insurance_refunded_at) {
            return 'Insurance refunded on ' . $this->insurance_refunded_at->format('M d, Y');
        }

        if ($this->isDelivered()) {
            return 'Insurance refund available';
        }

        return 'Insurance paid - refundable after delivery';
    }

    /**
     * Get diplomatic envelope status
     */
    public function getDiplomaticEnvelopeStatus(): string
    {
        if (!$this->has_diplomatic_envelope) {
            return 'Standard package';
        }

        return 'Includes diplomatic envelope (+' . \App\Helpers\CurrencyHelper::format($this->diplomatic_envelope_cost) . ')';
    }

    /**
     * Get all special services as formatted list
     */
    public function getSpecialServicesFormatted(): array
    {
        $services = [];

        if ($this->requires_insurance) {
            $services[] = [
                'name' => 'Refundable Insurance',
                'cost' => $this->insurance_cost,
                'status' => $this->getInsuranceStatusMessage(),
                'paid' => $this->insurance_paid,
                'refunded' => (bool) $this->insurance_refunded_at,
            ];
        }

        if ($this->has_diplomatic_envelope) {
            $services[] = [
                'name' => 'Diplomatic Envelope',
                'cost' => $this->diplomatic_envelope_cost,
                'status' => 'Included in package',
                'details' => $this->diplomatic_envelope_details,
            ];
        }

        // Add any additional special services from JSON field
        if ($this->special_services) {
            foreach ($this->special_services as $service) {
                $services[] = $service;
            }
        }

        return $services;
    }

    /**
     * Add customer alert
     */
    public function addCustomerAlert(string $type, string $message, array $data = []): void
    {
        $alerts = $this->customer_alerts ?? [];
        $alerts[] = [
            'type' => $type,
            'message' => $message,
            'data' => $data,
            'created_at' => now()->toISOString(),
        ];

        $this->update(['customer_alerts' => $alerts]);
    }

    /**
     * Get pending customer alerts
     */
    public function getPendingAlerts(): array
    {
        $alerts = [];

        if ($this->requiresInsurancePayment() && !$this->customer_notified_insurance) {
            $alerts[] = [
                'type' => 'insurance_required',
                'title' => 'Insurance Payment Required',
                'message' => 'Your package requires a refundable insurance fee of ' .
                           \App\Helpers\CurrencyHelper::format($this->insurance_cost) .
                           ' before delivery. This fee will be refunded after successful delivery.',
                'priority' => 'high',
                'action_required' => true,
            ];
        }

        if ($this->has_diplomatic_envelope && !$this->customer_notified_diplomatic) {
            $alerts[] = [
                'type' => 'diplomatic_envelope',
                'title' => 'Diplomatic Envelope Included',
                'message' => 'Your package includes a diplomatic envelope service for enhanced security and priority handling.',
                'priority' => 'medium',
                'action_required' => false,
            ];
        }

        return $alerts;
    }

    /**
     * Mark customer as notified for specific alert type
     */
    public function markCustomerNotified(string $type): void
    {
        $field = 'customer_notified_' . $type;
        if (in_array($field, $this->fillable)) {
            $this->update([$field => true]);
        }
    }

    /**
     * Check if parcel requires admin approval
     */
    public function requiresApproval(): bool
    {
        return $this->requires_admin_approval && !$this->is_approved;
    }

    /**
     * Approve the parcel
     */
    public function approve(User $approver, string $notes = null): void
    {
        $this->update([
            'is_approved' => true,
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);

        // Create tracking event for approval
        $this->trackingEvents()->create([
            'status' => 'approved',
            'location' => 'Admin Office',
            'description' => 'Package approved by admin and ready for processing',
            'event_date' => now(),
            'is_public' => true,
        ]);
    }

    /**
     * Get approval status message
     */
    public function getApprovalStatusMessage(): string
    {
        if (!$this->requires_admin_approval) {
            return 'No approval required';
        }

        if ($this->is_approved) {
            return 'Approved on ' . $this->approved_at->format('M d, Y') .
                   ($this->approvedBy ? ' by ' . $this->approvedBy->name : '');
        }

        return 'Pending admin approval';
    }

    /**
     * Scope to get parcels pending approval
     */
    public function scopePendingApproval($query)
    {
        return $query->where('requires_admin_approval', true)
                    ->where('is_approved', false);
    }

    /**
     * Scope to get approved parcels
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }
}
