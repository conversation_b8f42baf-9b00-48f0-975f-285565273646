@extends('layouts.admin')

@section('title', isset($parcel) ? 'Edit Parcel - ' . $parcel->tracking_number : 'Create New Parcel')
@section('page-title', isset($parcel) ? 'Edit Parcel' : 'Create New Parcel')

@section('page-actions')
    <div class="btn-group" role="group">
        @if(isset($parcel))
            <a href="{{ route('admin.parcels.show', $parcel) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i> View Details
            </a>
        @endif
        <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to List
        </a>
        @if(!isset($parcel))
            <button type="button" class="btn btn-outline-info" onclick="clearForm()">
                <i class="fas fa-eraser me-1"></i> Clear Form
            </button>
        @endif
    </div>
@endsection

@section('content')
    <form method="POST" action="{{ isset($parcel) ? route('admin.parcels.update', $parcel) : route('admin.parcels.store') }}" id="parcelForm">
        @csrf
        @if(isset($parcel))
            @method('PUT')
        @endif
        
        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <!-- Sender Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Sender Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sender_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_name') is-invalid @enderror"
                                       id="sender_name" name="sender_name" value="{{ old('sender_name', $parcel->sender_name ?? '') }}" required>
                                @error('sender_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sender_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('sender_email') is-invalid @enderror" 
                                       id="sender_email" name="sender_email" value="{{ old('sender_email') }}" required>
                                @error('sender_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sender_phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('sender_phone') is-invalid @enderror" 
                                       id="sender_phone" name="sender_phone" value="{{ old('sender_phone') }}" required>
                                @error('sender_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sender_country" class="form-label">Country <span class="text-danger">*</span></label>
                                <select class="form-select @error('sender_country') is-invalid @enderror" 
                                        id="sender_country" name="sender_country" required>
                                    <option value="">Select Country</option>
                                    <option value="USA" {{ old('sender_country') === 'USA' ? 'selected' : '' }}>United States</option>
                                    <option value="Canada" {{ old('sender_country') === 'Canada' ? 'selected' : '' }}>Canada</option>
                                    <option value="Mexico" {{ old('sender_country') === 'Mexico' ? 'selected' : '' }}>Mexico</option>
                                    <option value="UK" {{ old('sender_country') === 'UK' ? 'selected' : '' }}>United Kingdom</option>
                                </select>
                                @error('sender_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="sender_address" class="form-label">Street Address <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('sender_address') is-invalid @enderror" 
                                      id="sender_address" name="sender_address" rows="2" required>{{ old('sender_address') }}</textarea>
                            @error('sender_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="sender_city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_city') is-invalid @enderror" 
                                       id="sender_city" name="sender_city" value="{{ old('sender_city') }}" required>
                                @error('sender_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sender_state" class="form-label">State/Province <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_state') is-invalid @enderror" 
                                       id="sender_state" name="sender_state" value="{{ old('sender_state') }}" required>
                                @error('sender_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sender_postal_code" class="form-label">Postal Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('sender_postal_code') is-invalid @enderror" 
                                       id="sender_postal_code" name="sender_postal_code" value="{{ old('sender_postal_code') }}" required>
                                @error('sender_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recipient Information -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>Recipient Information
                        </h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="copyFromSender()">
                            <i class="fas fa-copy me-1"></i> Copy from Sender
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="recipient_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_name') is-invalid @enderror" 
                                       id="recipient_name" name="recipient_name" value="{{ old('recipient_name') }}" required>
                                @error('recipient_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="recipient_email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('recipient_email') is-invalid @enderror" 
                                       id="recipient_email" name="recipient_email" value="{{ old('recipient_email') }}" required>
                                @error('recipient_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="recipient_phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control @error('recipient_phone') is-invalid @enderror" 
                                       id="recipient_phone" name="recipient_phone" value="{{ old('recipient_phone') }}" required>
                                @error('recipient_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="recipient_country" class="form-label">Country <span class="text-danger">*</span></label>
                                <select class="form-select @error('recipient_country') is-invalid @enderror" 
                                        id="recipient_country" name="recipient_country" required>
                                    <option value="">Select Country</option>
                                    <option value="USA" {{ old('recipient_country') === 'USA' ? 'selected' : '' }}>United States</option>
                                    <option value="Canada" {{ old('recipient_country') === 'Canada' ? 'selected' : '' }}>Canada</option>
                                    <option value="Mexico" {{ old('recipient_country') === 'Mexico' ? 'selected' : '' }}>Mexico</option>
                                    <option value="UK" {{ old('recipient_country') === 'UK' ? 'selected' : '' }}>United Kingdom</option>
                                </select>
                                @error('recipient_country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="recipient_address" class="form-label">Street Address <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('recipient_address') is-invalid @enderror" 
                                      id="recipient_address" name="recipient_address" rows="2" required>{{ old('recipient_address') }}</textarea>
                            @error('recipient_address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="recipient_city" class="form-label">City <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_city') is-invalid @enderror" 
                                       id="recipient_city" name="recipient_city" value="{{ old('recipient_city') }}" required>
                                @error('recipient_city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="recipient_state" class="form-label">State/Province <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_state') is-invalid @enderror" 
                                       id="recipient_state" name="recipient_state" value="{{ old('recipient_state') }}" required>
                                @error('recipient_state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="recipient_postal_code" class="form-label">Postal Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('recipient_postal_code') is-invalid @enderror" 
                                       id="recipient_postal_code" name="recipient_postal_code" value="{{ old('recipient_postal_code') }}" required>
                                @error('recipient_postal_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-box me-2"></i>Package Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="description" class="form-label">Package Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" required 
                                      placeholder="Describe the contents of the package...">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="weight" class="form-label">Weight (kg) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('weight') is-invalid @enderror" 
                                       id="weight" name="weight" value="{{ old('weight') }}" required>
                                @error('weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="dimensions" class="form-label">Dimensions (L×W×H)</label>
                                <input type="text" class="form-control @error('dimensions') is-invalid @enderror" 
                                       id="dimensions" name="dimensions" value="{{ old('dimensions') }}" 
                                       placeholder="e.g., 30×20×10 cm">
                                @error('dimensions')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="declared_value" class="form-label">Declared Value ($) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('declared_value') is-invalid @enderror" 
                                       id="declared_value" name="declared_value" value="{{ old('declared_value') }}" required>
                                @error('declared_value')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="special_instructions" class="form-label">Special Instructions</label>
                            <textarea class="form-control @error('special_instructions') is-invalid @enderror" 
                                      id="special_instructions" name="special_instructions" rows="2" 
                                      placeholder="Any special handling instructions...">{{ old('special_instructions') }}</textarea>
                            @error('special_instructions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Shipping Options -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-shipping-fast me-2"></i>Shipping Options
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="carrier_id" class="form-label">Carrier <span class="text-danger">*</span></label>
                            <select class="form-select @error('carrier_id') is-invalid @enderror" 
                                    id="carrier_id" name="carrier_id" required onchange="updateShippingCost()">
                                <option value="">Select Carrier</option>
                                @foreach($carriers as $carrier)
                                    <option value="{{ $carrier->id }}" 
                                            data-base-rate="{{ $carrier->base_rate }}" 
                                            data-per-kg-rate="{{ $carrier->per_kg_rate }}"
                                            {{ old('carrier_id') == $carrier->id ? 'selected' : '' }}>
                                        {{ $carrier->name }} ({{ $carrier->code }})
                                    </option>
                                @endforeach
                            </select>
                            @error('carrier_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="service_type" class="form-label">Service Type <span class="text-danger">*</span></label>
                            <select class="form-select @error('service_type') is-invalid @enderror" 
                                    id="service_type" name="service_type" required onchange="updateShippingCost()">
                                <option value="">Select Service</option>
                                <option value="standard" {{ old('service_type') === 'standard' ? 'selected' : '' }}>Standard</option>
                                <option value="express" {{ old('service_type') === 'express' ? 'selected' : '' }}>Express (+20%)</option>
                                <option value="overnight" {{ old('service_type') === 'overnight' ? 'selected' : '' }}>Overnight (+50%)</option>
                                <option value="same_day" {{ old('service_type') === 'same_day' ? 'selected' : '' }}>Same Day (+100%)</option>
                            </select>
                            @error('service_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="estimated_delivery_date" class="form-label">Estimated Delivery Date</label>
                            <input type="date" class="form-control @error('estimated_delivery_date') is-invalid @enderror" 
                                   id="estimated_delivery_date" name="estimated_delivery_date" 
                                   value="{{ old('estimated_delivery_date') }}" min="{{ date('Y-m-d', strtotime('+1 day')) }}">
                            @error('estimated_delivery_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Cost Calculation -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-calculator me-2"></i>Cost Calculation
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="shipping_cost" class="form-label">Shipping Cost ($) <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" min="0" class="form-control @error('shipping_cost') is-invalid @enderror" 
                                   id="shipping_cost" name="shipping_cost" value="{{ old('shipping_cost') }}" required readonly>
                            @error('shipping_cost')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Calculated automatically based on weight and carrier</small>
                        </div>
                        <div class="mb-3">
                            <label for="insurance_cost" class="form-label">Insurance Cost ($)</label>
                            <input type="number" step="0.01" min="0" class="form-control @error('insurance_cost') is-invalid @enderror"
                                   id="insurance_cost" name="insurance_cost" value="{{ old('insurance_cost', '0') }}" onchange="updateTotalCost()">
                            @error('insurance_cost')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="requires_insurance"
                                           name="requires_insurance" value="1" {{ old('requires_insurance', isset($parcel) && $parcel->requires_insurance) ? 'checked' : '' }}
                                           onchange="toggleRefundableInsuranceFields()">
                                    <label class="form-check-label" for="requires_insurance">
                                        <strong>Requires Refundable Insurance</strong>
                                        <br><small class="text-muted">Customer must pay insurance before delivery (refundable)</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="has_diplomatic_envelope"
                                           name="has_diplomatic_envelope" value="1" {{ old('has_diplomatic_envelope', isset($parcel) && $parcel->has_diplomatic_envelope) ? 'checked' : '' }}
                                           onchange="toggleDiplomaticEnvelopeFields()">
                                    <label class="form-check-label" for="has_diplomatic_envelope">
                                        <strong>Diplomatic Envelope</strong>
                                        <br><small class="text-muted">Enhanced security and priority handling</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="refundable_insurance_fields" style="display: none;">
                            <div class="mb-3">
                                <label for="refundable_insurance_cost" class="form-label">Refundable Insurance Cost ($) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('refundable_insurance_cost') is-invalid @enderror"
                                       id="refundable_insurance_cost" name="refundable_insurance_cost"
                                       value="{{ old('refundable_insurance_cost', $parcel->refundable_insurance_cost ?? '') }}"
                                       min="0" step="0.01" onchange="updateTotalCost()">
                                @error('refundable_insurance_cost')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Typically 10% of declared value. Adjustable.</small>
                            </div>
                        </div>

                        <div id="diplomatic_envelope_fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="diplomatic_envelope_cost" class="form-label">Diplomatic Envelope Cost ($)</label>
                                    <input type="number" class="form-control @error('diplomatic_envelope_cost') is-invalid @enderror"
                                           id="diplomatic_envelope_cost" name="diplomatic_envelope_cost"
                                           value="{{ old('diplomatic_envelope_cost', $parcel->diplomatic_envelope_cost ?? '') }}"
                                           min="0" step="0.01" onchange="updateTotalCost()">
                                    @error('diplomatic_envelope_cost')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="diplomatic_envelope_details" class="form-label">Envelope Details</label>
                                    <input type="text" class="form-control @error('diplomatic_envelope_details') is-invalid @enderror"
                                           id="diplomatic_envelope_details" name="diplomatic_envelope_details"
                                           value="{{ old('diplomatic_envelope_details', $parcel->diplomatic_envelope_details ?? '') }}"
                                           placeholder="e.g., Embassy documents, Official correspondence">
                                    @error('diplomatic_envelope_details')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Total Cost ($)</label>
                            <div class="form-control bg-light" id="total_cost_display">{{ \App\Helpers\CurrencyHelper::format(0) }}</div>
                        </div>
                    </div>
                </div>

                <!-- Customer Assignment -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-user me-2"></i>Customer Assignment
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Assign to Customer</label>
                            <select class="form-select @error('user_id') is-invalid @enderror" 
                                    id="user_id" name="user_id">
                                <option value="">No Customer (Guest)</option>
                                @foreach($customers as $customer) {{-- Assuming $customers is passed for edit too --}}
                                    <option value="{{ $customer->id }}" {{ old('user_id') == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->name }} ({{ $customer->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Optional: Link this parcel to an existing customer</small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>{{ isset($parcel) ? 'Update Parcel' : 'Create Parcel' }}
                            </button>
                            <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('scripts')
<script>
    // Auto-calculate shipping cost based on weight, carrier, and service type
    function updateShippingCost() {
        const carrierSelect = document.getElementById('carrier_id');
        const serviceSelect = document.getElementById('service_type');
        const weightInput = document.getElementById('weight');
        const shippingCostInput = document.getElementById('shipping_cost');
        
        if (carrierSelect.value && weightInput.value && serviceSelect.value) {
            const selectedOption = carrierSelect.options[carrierSelect.selectedIndex];
            const baseRate = parseFloat(selectedOption.dataset.baseRate) || 0;
            const perKgRate = parseFloat(selectedOption.dataset.perKgRate) || 0;
            const weight = parseFloat(weightInput.value) || 0;
            
            let cost = baseRate + (perKgRate * weight);
            
            // Apply service type multiplier (from admin settings)
            switch (serviceSelect.value) {
                case 'express':
                    cost *= {{ \App\Models\SiteSetting::getValue('shipping_service_multiplier_express', '1.2') }};
                    break;
                case 'overnight':
                    cost *= {{ \App\Models\SiteSetting::getValue('shipping_service_multiplier_overnight', '1.5') }};
                    break;
                case 'same_day':
                    cost *= {{ \App\Models\SiteSetting::getValue('shipping_service_multiplier_same_day', '2.0') }};
                    break;
            }
            
            shippingCostInput.value = cost.toFixed(2);
            updateTotalCost();
        }
    }
    
    // Update total cost
    function updateTotalCost() {
        const shippingCost = parseFloat(document.getElementById('shipping_cost').value) || 0;
        const insuranceCost = parseFloat(document.getElementById('insurance_cost').value) || 0;
        const diplomaticCost = parseFloat(document.getElementById('diplomatic_envelope_cost').value) || 0;
        const refundableInsuranceCostInput = document.getElementById('refundable_insurance_cost');
        let refundableInsuranceCost = 0;

        if (refundableInsuranceCostInput && refundableInsuranceCostInput.closest('#refundable_insurance_fields').style.display !== 'none') {
            refundableInsuranceCost = parseFloat(refundableInsuranceCostInput.value) || 0;
        }
        
        const totalCost = shippingCost + insuranceCost + diplomaticCost + refundableInsuranceCost;

        document.getElementById('total_cost_display').textContent = formatCurrency(totalCost);
    }

    // Toggle refundable insurance fields
    function toggleRefundableInsuranceFields() {
        const checkbox = document.getElementById('requires_insurance');
        const fieldsDiv = document.getElementById('refundable_insurance_fields');
        const costInput = document.getElementById('refundable_insurance_cost');
        const declaredValueInput = document.getElementById('declared_value');

        if (checkbox.checked) {
            fieldsDiv.style.display = 'block';
            if (!costInput.value && declaredValueInput.value) {
                const declaredValue = parseFloat(declaredValueInput.value) || 0;
                costInput.value = (declaredValue * 0.10).toFixed(2); // Default to 10% of declared value
            } else if (!costInput.value) {
                costInput.value = '0.00'; // Fallback if declared value is also empty
            }
        } else {
            fieldsDiv.style.display = 'none';
            costInput.value = ''; // Clear the cost when unchecked
        }
        updateTotalCost();
    }

    // Toggle diplomatic envelope fields
    function toggleDiplomaticEnvelopeFields() {
        const checkbox = document.getElementById('has_diplomatic_envelope');
        const fields = document.getElementById('diplomatic_envelope_fields');

        if (checkbox.checked) {
            fields.style.display = 'block';
            // Set default cost if empty
            const diplomaticCostField = document.getElementById('diplomatic_envelope_cost');
            if (!diplomaticCostField.value) {
                diplomaticCostField.value = '{{ \App\Models\SiteSetting::getValue("default_diplomatic_envelope_cost", "50.00") }}';
            }
        } else {
            fields.style.display = 'none';
            document.getElementById('diplomatic_envelope_cost').value = ''; // Clear the cost
            document.getElementById('diplomatic_envelope_details').value = '';
        }
        updateTotalCost();
    }
    
    // Copy sender information to recipient
    function copyFromSender() {
        const fields = ['name', 'email', 'phone', 'address', 'city', 'state', 'postal_code', 'country'];
        
        fields.forEach(field => {
            const senderField = document.getElementById('sender_' + field);
            const recipientField = document.getElementById('recipient_' + field);
            
            if (senderField && recipientField) {
                recipientField.value = senderField.value;
            }
        });
    }
    
    // Clear entire form
    function clearForm() {
        if (confirm('Are you sure you want to clear all form data?')) {
            document.getElementById('parcelForm').reset();
            // Manually ensure toggled fields are reset and hidden
            document.getElementById('requires_insurance').checked = false;
            toggleRefundableInsuranceFields(); 
            document.getElementById('has_diplomatic_envelope').checked = false;
            toggleDiplomaticEnvelopeFields();
            
            updateTotalCost(); // Recalculate to show formatted $0.00
        }
    }
    
    // Event listeners
    document.getElementById('weight').addEventListener('input', updateShippingCost);
    document.getElementById('insurance_cost').addEventListener('input', updateTotalCost);
    document.getElementById('refundable_insurance_cost').addEventListener('input', updateTotalCost);
    document.getElementById('diplomatic_envelope_cost').addEventListener('input', updateTotalCost);
    document.getElementById('declared_value').addEventListener('input', () => {
        if (document.getElementById('requires_insurance').checked) {
            // Re-evaluate default refundable insurance cost if the field is empty or was auto-filled
            const costInput = document.getElementById('refundable_insurance_cost');
            const declaredValueInput = document.getElementById('declared_value');
            const declaredValue = parseFloat(declaredValueInput.value) || 0;
            // A simple approach: if cost is 0 or was 10% of previous declared value, update it.
            // For simplicity, just call toggle which has logic to set default if costInput is empty.
            toggleRefundableInsuranceFields();
        }
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleDiplomaticEnvelopeFields(); // Initialize diplomatic fields and call updateTotalCost
        toggleRefundableInsuranceFields(); // Initialize refundable insurance fields and call updateTotalCost
        updateShippingCost();
    });
</script>
@endpush
