<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\View\View;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     */
    public function create(): View
    {
        return view('auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => ['required', 'email'],
        ]);

        // Check if user exists and is active before attempting to send reset link
        $user = \App\Models\User::where('email', $request->email)
                                ->where('is_active', true)
                                ->first();

        // Always show the same generic message for security reasons
        // This prevents email enumeration attacks
        $genericMessage = 'If your email address exists on our system, password reset instructions will be sent to your email address.';

        if ($user) {
            // Only send reset link if user exists and is active
            Password::sendResetLink(
                $request->only('email')
            );
        }

        // Always return success message regardless of whether email exists
        return back()->with('status', $genericMessage);
    }
}
