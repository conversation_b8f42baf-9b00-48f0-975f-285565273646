<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('parcels', function (Blueprint $table) {
            $table->boolean('requires_admin_approval')->default(false)->after('status');
            $table->boolean('is_approved')->default(false)->after('requires_admin_approval');
            $table->foreignId('approved_by')->nullable()->constrained('users')->after('is_approved');
            $table->timestamp('approved_at')->nullable()->after('approved_by');
            $table->text('approval_notes')->nullable()->after('approved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('parcels', function (Blueprint $table) {
            $table->dropForeign(['approved_by']);
            $table->dropColumn([
                'requires_admin_approval',
                'is_approved',
                'approved_by',
                'approved_at',
                'approval_notes'
            ]);
        });
    }
};
