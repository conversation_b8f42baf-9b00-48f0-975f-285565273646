@extends('layouts.admin')

@section('title', 'Create Invoice for Parcel ' . $parcel->tracking_number)
@section('page-title', 'Create Invoice')

@section('content')
<form method="POST" action="{{ route('admin.parcels.invoices.store', $parcel) }}" id="invoiceForm">
    @csrf
    <input type="hidden" name="parcel_id" value="{{ $parcel->id }}">

    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header"><h6 class="m-0 font-weight-bold text-primary">Invoice Details</h6></div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Parcel Tracking Number</label>
                            <input type="text" class="form-control bg-light" value="{{ $parcel->tracking_number }}" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="invoice_number" class="form-label">Invoice Number</label>
                            <input type="text" class="form-control bg-light @error('invoice_number') is-invalid @enderror" id="invoice_number" name="invoice_number" value="{{ old('invoice_number', \App\Models\Invoice::generateInvoiceNumber()) }}" readonly>
                            @error('invoice_number') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="issue_date" class="form-label">Issue Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('issue_date') is-invalid @enderror" id="issue_date" name="issue_date" value="{{ old('issue_date', now()->format('Y-m-d')) }}" required>
                            @error('issue_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="due_date" class="form-label">Due Date</label>
                            <input type="date" class="form-control @error('due_date') is-invalid @enderror" id="due_date" name="due_date" value="{{ old('due_date') }}">
                            @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header"><h6 class="m-0 font-weight-bold text-primary">Bill To</h6></div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="issued_to_type" class="form-label">Invoice For <span class="text-danger">*</span></label>
                        <select class="form-select @error('issued_to_type') is-invalid @enderror" id="issued_to_type" name="issued_to_type" required>
                            <option value="sender" {{ old('issued_to_type') == 'sender' ? 'selected' : '' }}>Sender</option>
                            <option value="recipient" {{ old('issued_to_type') == 'recipient' ? 'selected' : '' }}>Recipient</option>
                            <option value="other" {{ old('issued_to_type') == 'other' ? 'selected' : '' }}>Other</option>
                        </select>
                        @error('issued_to_type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div id="bill_to_details">
                        <div class="mb-3">
                            <label for="issued_to_name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('issued_to_name') is-invalid @enderror" id="issued_to_name" name="issued_to_name" value="{{ old('issued_to_name') }}" required>
                            @error('issued_to_name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label for="issued_to_email" class="form-label">Email</label>
                            <input type="email" class="form-control @error('issued_to_email') is-invalid @enderror" id="issued_to_email" name="issued_to_email" value="{{ old('issued_to_email') }}">
                            @error('issued_to_email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label for="issued_to_address" class="form-label">Address</label>
                            <textarea class="form-control @error('issued_to_address') is-invalid @enderror" id="issued_to_address" name="issued_to_address" rows="3">{{ old('issued_to_address') }}</textarea>
                            @error('issued_to_address') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Invoice Items</h6>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="addItemBtn"><i class="fas fa-plus"></i> Add Item</button>
                </div>
                <div class="card-body">
                    <div id="invoice_items_container" class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Description <span class="text-danger">*</span></th>
                                    <th width="100">Qty <span class="text-danger">*</span></th>
                                    <th width="150">Unit Price <span class="text-danger">*</span></th>
                                    <th width="150">Amount</th>
                                    <th width="50"></th>
                                </tr>
                            </thead>
                            <tbody id="invoice_items_tbody">
                                {{-- Items will be added here by JavaScript --}}
                            </tbody>
                        </table>
                    </div>
                     @error('items') <div class="text-danger mt-2">{{ $message }}</div> @enderror
                     @error('items.*.description') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                     @error('items.*.quantity') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                     @error('items.*.unit_price') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header"><h6 class="m-0 font-weight-bold text-primary">Summary & Actions</h6></div>
                <div class="card-body">
                    <table class="table">
                        <tr>
                            <td>Subtotal:</td>
                            <td class="text-end" id="sub_total_display">@currency(0)</td>
                        </tr>
                        <tr>
                            <td>Tax (%):</td>
                            <td class="text-end">
                                <input type="number" step="0.01" min="0" class="form-control form-control-sm text-end d-inline-block" style="width: 80px;" id="tax_percentage" value="0">
                            </td>
                        </tr>
                        <tr>
                            <td>Tax Amount:</td>
                            <td class="text-end" id="tax_amount_display">@currency(0)</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Total:</td>
                            <td class="text-end fw-bold fs-5" id="total_amount_display">@currency(0)</td>
                        </tr>
                    </table>
                    <input type="hidden" name="sub_total" id="sub_total_input" value="0">
                    <input type="hidden" name="tax_amount" id="tax_amount_input" value="0">
                    <input type="hidden" name="total_amount" id="total_amount_input" value="0">

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                            <option value="draft" {{ old('status', 'draft') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="sent" {{ old('status') == 'sent' ? 'selected' : '' }}>Sent</option>
                            <option value="paid" {{ old('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                        </select>
                        @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (Internal or for Customer)</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                        @error('notes') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div class="mb-3">
                        <label for="payment_instructions" class="form-label">Payment Instructions</label>
                        <textarea class="form-control @error('payment_instructions') is-invalid @enderror" id="payment_instructions" name="payment_instructions" rows="3">{{ old('payment_instructions', \App\Models\SiteSetting::getValue('default_payment_instructions', '')) }}</textarea>
                        @error('payment_instructions') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save me-2"></i>Create Invoice</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
    const parcel = @json($parcel);
    const issuedToTypeSelect = document.getElementById('issued_to_type');
    const issuedToNameInput = document.getElementById('issued_to_name');
    const issuedToEmailInput = document.getElementById('issued_to_email');
    const issuedToAddressInput = document.getElementById('issued_to_address');

    function populateBillTo(type) {
        if (type === 'sender') {
            issuedToNameInput.value = parcel.sender_name || '';
            issuedToEmailInput.value = parcel.sender_email || '';
            issuedToAddressInput.value = `${parcel.sender_address || ''}\n${parcel.sender_city || ''}, ${parcel.sender_state || ''} ${parcel.sender_postal_code || ''}\n${parcel.sender_country || ''}`.trim();
        } else if (type === 'recipient') {
            issuedToNameInput.value = parcel.recipient_name || '';
            issuedToEmailInput.value = parcel.recipient_email || '';
            issuedToAddressInput.value = `${parcel.recipient_address || ''}\n${parcel.recipient_city || ''}, ${parcel.recipient_state || ''} ${parcel.recipient_postal_code || ''}\n${parcel.recipient_country || ''}`.trim();
        } else { // other
            issuedToNameInput.value = '';
            issuedToEmailInput.value = '';
            issuedToAddressInput.value = '';
        }
    }

    issuedToTypeSelect.addEventListener('change', function() {
        populateBillTo(this.value);
    });
    populateBillTo(issuedToTypeSelect.value); // Initial population

    const itemsTbody = document.getElementById('invoice_items_tbody');
    const taxPercentageInput = document.getElementById('tax_percentage'); // Moved declaration up
    taxPercentageInput.addEventListener('input', updateTotals); // Attach listener early

    const addItemBtn = document.getElementById('addItemBtn');
    let itemIndex = 0;

    function addInvoiceItem(description = '', quantity = 1, unitPrice = 0) {
        itemIndex++;
        const newRow = itemsTbody.insertRow();
        newRow.innerHTML = `
            <td><input type="text" name="items[${itemIndex}][description]" class="form-control form-control-sm" value="${description}" required></td>
            <td><input type="number" name="items[${itemIndex}][quantity]" class="form-control form-control-sm item-quantity" value="${quantity}" min="0.01" step="0.01" required></td>
            <td><input type="number" name="items[${itemIndex}][unit_price]" class="form-control form-control-sm item-unit-price" value="${unitPrice.toFixed(2)}" min="0" step="0.01" required></td>
            <td class="item-amount text-end">${formatCurrency(quantity * unitPrice)}</td>
            <td><button type="button" class="btn btn-sm btn-danger removeItemBtn"><i class="fas fa-times"></i></button></td>
        `;
        attachItemListeners(newRow);
        updateTotals();
    }

    function attachItemListeners(row) {
        row.querySelector('.removeItemBtn').addEventListener('click', function() {
            this.closest('tr').remove();
            updateTotals();
        });
        row.querySelectorAll('.item-quantity, .item-unit-price').forEach(input => {
            input.addEventListener('input', updateTotals);
        });
    }

    addItemBtn.addEventListener('click', () => addInvoiceItem());

    // Pre-populate items from parcel costs
    if (parcel.shipping_cost > 0) {
        addInvoiceItem('Shipping Cost', 1, parseFloat(parcel.shipping_cost));
    }
    if (parcel.insurance_cost > 0) {
        addInvoiceItem('Standard Insurance Cost', 1, parseFloat(parcel.insurance_cost));
    }
    if (parcel.requires_insurance && parcel.refundable_insurance_cost > 0) {
        addInvoiceItem('Refundable Insurance Cost', 1, parseFloat(parcel.refundable_insurance_cost));
    }
    if (parcel.has_diplomatic_envelope && parcel.diplomatic_envelope_cost > 0) {
        addInvoiceItem('Diplomatic Envelope Cost', 1, parseFloat(parcel.diplomatic_envelope_cost));
    }
    // Add at least one empty row if no costs were pre-populated
    if (itemsTbody.rows.length === 0) {
        addInvoiceItem();
    }


    function updateTotals() {
        let subTotal = 0;
        itemsTbody.querySelectorAll('tr').forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const unitPrice = parseFloat(row.querySelector('.item-unit-price').value) || 0;
            const amount = quantity * unitPrice;
            row.querySelector('.item-amount').textContent = formatCurrency(amount);
            subTotal += amount;
        });

        const taxPercentage = parseFloat(taxPercentageInput.value) || 0;
        const taxAmount = subTotal * (taxPercentage / 100);
        const totalAmount = subTotal + taxAmount;

        document.getElementById('sub_total_display').textContent = formatCurrency(subTotal);
        document.getElementById('tax_amount_display').textContent = formatCurrency(taxAmount);
        document.getElementById('total_amount_display').textContent = formatCurrency(totalAmount);

        document.getElementById('sub_total_input').value = subTotal.toFixed(2);
        document.getElementById('tax_amount_input').value = taxAmount.toFixed(2);
        document.getElementById('total_amount_input').value = totalAmount.toFixed(2);
    }
    updateTotals(); // Initial calculation
});
</script>
@endpush
