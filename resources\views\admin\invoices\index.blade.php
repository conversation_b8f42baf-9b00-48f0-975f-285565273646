@extends('layouts.admin')

@section('title', 'Manage Invoices')
@section('page-title', 'Invoices')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">All Invoices ({{ $invoices->total() }})</h6>
        {{-- Optional: Add a general "Create Invoice" button if needed, though typically created from a parcel --}}
    </div>
    <div class="card-body">
        @if($invoices->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Parcel #</th>
                            <th>Issued To</th>
                            <th>Issue Date</th>
                            <th>Due Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoices as $invoice)
                            <tr>
                                <td>
                                    <a href="{{ route('admin.invoices.show', $invoice) }}">
                                        <strong>{{ $invoice->invoice_number }}</strong>
                                    </a>
                                </td>
                                <td>
                                    <a href="{{ route('admin.parcels.show', $invoice->parcel) }}">
                                        {{ $invoice->parcel->tracking_number }}
                                    </a>
                                </td>
                                <td>{{ $invoice->issued_to_name }} <br><small class="text-muted">{{ ucfirst($invoice->issued_to_type) }}</small></td>
                                <td>{{ $invoice->issue_date->format('M d, Y') }}</td>
                                <td>{{ $invoice->due_date ? $invoice->due_date->format('M d, Y') : 'N/A' }}</td>
                                <td>@currency($invoice->total_amount)</td>
                                <td>
                                    <span class="badge bg-{{ strtolower($invoice->status) == 'paid' ? 'success' : (strtolower($invoice->status) == 'draft' ? 'secondary' : 'warning') }}">
                                        {{ ucfirst($invoice->status) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.invoices.show', $invoice) }}" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.invoices.download', $invoice) }}" class="btn btn-sm btn-outline-info" title="Download PDF">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {{-- Add Edit/Delete if status allows --}}
                                        @if($invoice->status === 'draft')
                                        <form action="{{ route('admin.invoices.destroy', $invoice) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this invoice?');" style="display:inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <div class="mt-4">
                {{ $invoices->links('pagination.admin') }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-file-invoice-dollar fa-4x text-muted mb-4"></i>
                <h5 class="text-muted">No invoices found.</h5>
            </div>
        @endif
    </div>
</div>
@endsection
