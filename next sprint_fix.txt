Do not validate emails when users (admin and customers are resseting password). 
Instead always say "if your email address exist on our system, Password reset instructions will be sent to your email address.". Validating emails and telling a user they don't exist poses a security risk where hacker or malicious users get to know which emails are on the system.

when is user's account is deleted (is_active = 0), we validate it as an account that does not exist, currently quite, does not send any errors to the user. 
implement last_login_at and last_login_ip (table already has fields).

Implement the customer create package which initially is set to inactive until approve by an admin. 

Just like the 404 page when a user hits a 404 page, implement other pages like 500, 403, 401, 402.
Check how the 404 page is created and implemented. 



---------------------------------
fix the layout of the track button on the /track page.
make it stack the same like the area on the home page. 