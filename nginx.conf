server {
    listen 80;
    server_name routiselogistic.com;
    root /var/www/html/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    # Handle Vite assets
    location /build/ {
        alias /var/www/html/public/build/;
        try_files $uri $uri/ /index.php?$query_string;

        # Set proper MIME types
        types {
            text/css css;
            application/javascript js;
            application/json json;
            image/svg+xml svg svgz;
        }

        # Enable gzip compression
        gzip on;
        gzip_types text/css application/javascript application/json image/svg+xml;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
