<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'parcel_id',
        'invoice_number',
        'issued_to_type',
        'issued_to_name',
        'issued_to_email',
        'issued_to_address',
        'issue_date',
        'due_date',
        'sub_total',
        'tax_amount',
        'total_amount',
        'status',
        'notes',
        'payment_instructions',
        'created_by_id',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'sub_total' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function parcel(): BelongsTo
    {
        return $this->belongsTo(Parcel::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public static function generateInvoiceNumber(): string
    {
        $prefix = SiteSetting::getValue('invoice_prefix', 'INV');
        $datePart = now()->format('Ymd');
        $lastInvoice = self::where('invoice_number', 'like', "{$prefix}-{$datePart}-%")->orderBy('invoice_number', 'desc')->first();
        $nextId = 1;

        if ($lastInvoice) {
            $parts = explode('-', $lastInvoice->invoice_number);
            $lastId = (int)end($parts);
            $nextId = $lastId + 1;
        }
        return sprintf('%s-%s-%04d', $prefix, $datePart, $nextId);
    }
}

