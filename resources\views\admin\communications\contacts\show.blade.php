@extends('layouts.admin')

@section('title', 'Contact Message - ' . $contact->name)

@section('page-header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Contact Message</h1>
            <p class="text-muted">From {{ $contact->name }} - {{ $contact->created_at->format('M j, Y g:i A') }}</p>
        </div>
        <div>
            <a href="{{ route('admin.communications.contacts.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Messages
            </a>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Contact Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-envelope me-2"></i>
                        Message Details
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>From:</strong></div>
                        <div class="col-sm-9">
                            {{ $contact->name }}
                            <br><a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a>
                            @if($contact->phone)
                                <br><a href="tel:{{ $contact->phone }}">{{ $contact->phone }}</a>
                            @endif
                        </div>
                    </div>

                    @if($contact->subject)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Subject:</strong></div>
                        <div class="col-sm-9">{{ $contact->subject }}</div>
                    </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Status:</strong></div>
                        <div class="col-sm-9">
                            <span class="badge text-dark {{ $contact->status_badge }}">
                                {{ ucfirst($contact->status) }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Received:</strong></div>
                        <div class="col-sm-9">{{ $contact->created_at->format('F j, Y \a\t g:i A') }}</div>
                    </div>

                    @if($contact->read_at)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Read:</strong></div>
                        <div class="col-sm-9">{{ $contact->read_at->format('F j, Y \a\t g:i A') }}</div>
                    </div>
                    @endif

                    @if($contact->replied_at && $contact->repliedBy)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Replied:</strong></div>
                        <div class="col-sm-9">
                            {{ $contact->replied_at->format('F j, Y \a\t g:i A') }}
                            by {{ $contact->repliedBy->name }}
                        </div>
                    </div>
                    @endif

                    <hr>

                    <div class="row">
                        <div class="col-sm-3"><strong>Message:</strong></div>
                        <div class="col-sm-9">
                            <div class="bg-light p-3 rounded">
                                {!! nl2br(e($contact->message)) !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Reply Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-reply me-2"></i>
                        Quick Reply
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Quick Reply:</strong> Click the button below to compose a reply email in your default email client.
                    </div>
                    
                    <a href="mailto:{{ $contact->email }}?subject=Re: {{ $contact->subject ?: 'Your inquiry' }}&body=Dear {{ $contact->name }},%0D%0A%0D%0AThank you for contacting us.%0D%0A%0D%0ABest regards,%0D%0AThe Team" 
                       class="btn btn-primary">
                        <i class="fas fa-reply me-2"></i>
                        Reply via Email
                    </a>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status Management -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cog me-2"></i>
                        Manage Status
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.communications.contacts.update', $contact) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group mb-3">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="new" {{ $contact->status === 'new' ? 'selected' : '' }}>New</option>
                                <option value="read" {{ $contact->status === 'read' ? 'selected' : '' }}>Read</option>
                                <option value="replied" {{ $contact->status === 'replied' ? 'selected' : '' }}>Replied</option>
                                <option value="closed" {{ $contact->status === 'closed' ? 'selected' : '' }}>Closed</option>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label for="admin_notes">Admin Notes</label>
                            <textarea name="admin_notes" id="admin_notes" class="form-control" rows="4" 
                                      placeholder="Add internal notes about this contact...">{{ $contact->admin_notes }}</textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Update Status
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Additional Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>IP Address:</strong>
                        <br>{{ $contact->ip_address ?: 'Not recorded' }}
                    </div>
                    
                    @if($contact->user_agent)
                    <div class="mb-2">
                        <strong>Browser:</strong>
                        <br><small class="text-muted">{{ $contact->user_agent }}</small>
                    </div>
                    @endif

                    <div class="mb-2">
                        <strong>Message ID:</strong>
                        <br><code>#{{ $contact->id }}</code>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>
                        Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($contact->status === 'new')
                            <form method="POST" action="{{ route('admin.communications.contacts.mark-read', $contact) }}" style="display: inline;">
                                @csrf
                                <button type="submit" class="btn btn-info btn-sm w-100">
                                    <i class="fas fa-eye me-2"></i>
                                    Mark as Read
                                </button>
                            </form>
                        @endif

                        <a href="mailto:{{ $contact->email }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>
                            Send Email
                        </a>

                        <form method="POST" action="{{ route('admin.communications.contacts.destroy', $contact) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this contact message?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100">
                                <i class="fas fa-trash me-2"></i>
                                Delete Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
