<?php $__env->startSection('title', 'My Orders'); ?>
<?php $__env->startSection('page-title', 'My Orders'); ?>

<?php $__env->startSection('page-actions'); ?>
    <div class="btn-group" role="group">
        <a href="<?php echo e(route('customer.dashboard')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="<?php echo e(route('customer.support.create')); ?>" class="btn btn-primary">
            <i class="fas fa-headset me-1"></i> Get Support
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Order Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between text-dark align-items-center">
                    <div>
                        <h3 class="mb-1"><?php echo e($orders->total()); ?></h3>
                        <p class="mb-0">Total Orders</p>
                    </div>
                    <div>
                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1"><?php echo e($orders->where('status', 'pending')->count()); ?></h3>
                        <p class="mb-0">Pending</p>
                    </div>
                    <div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1"><?php echo e($orders->whereIn('status', ['shipped', 'processing'])->count()); ?></h3>
                        <p class="mb-0">In Progress</p>
                    </div>
                    <div>
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1"><?php echo e($orders->where('status', 'delivered')->count()); ?></h3>
                        <p class="mb-0">Delivered</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('customer.orders')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo e(request('search')); ?>" placeholder="Order number, product...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('status') === $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="payment_status" class="form-label">Payment</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">All Payments</option>
                        <?php $__currentLoopData = $paymentStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('payment_status') === $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo e(request('date_from')); ?>">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo e(request('date_to')); ?>">
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-receipt me-2"></i>
                Your Orders (<?php echo e($orders->total()); ?>)
            </h5>
        </div>
        
        <div class="card-body">
            <?php if($orders->count() > 0): ?>
                <div class="row">
                    <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12 mb-4">
                            <div class="card border">
                                <div class="card-header bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <strong>Order #<?php echo e($order->order_number); ?></strong>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-<?php echo e($order->status_badge_color); ?>">
                                                <?php echo e($order->formatted_status); ?>

                                            </span>
                                        </div>
                                        <div class="col-md-3">
                                            <span class="badge bg-<?php echo e($order->payment_status_badge_color); ?>">
                                                <?php echo e($order->formatted_payment_status); ?>

                                            </span>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <strong><?php echo \App\Helpers\CurrencyHelper::format($order->total_amount); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="row">
                                                <?php $__currentLoopData = $order->items->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-md-4 mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <?php if($item->product && $item->product->featured_image): ?>
                                                                <img src="<?php echo e(Storage::url($item->product->featured_image)); ?>" 
                                                                     alt="<?php echo e($item->product_name); ?>" 
                                                                     class="me-3" 
                                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                                            <?php else: ?>
                                                                <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                                     style="width: 50px; height: 50px;">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            <div>
                                                                <strong><?php echo e(Str::limit($item->product_name, 20)); ?></strong>
                                                                <br><small class="text-muted">Qty: <?php echo e($item->quantity); ?></small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                
                                                <?php if($order->items->count() > 3): ?>
                                                    <div class="col-md-4 mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                                 style="width: 50px; height: 50px;">
                                                                <i class="fas fa-plus text-muted"></i>
                                                            </div>
                                                            <div>
                                                                <strong>+<?php echo e($order->items->count() - 3); ?> more</strong>
                                                                <br><small class="text-muted">items</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="text-end">
                                                <p class="mb-1">
                                                    <strong>Order Date:</strong><br>
                                                    <?php echo e($order->created_at->format('M d, Y h:i A')); ?>

                                                </p>
                                                
                                                <?php if($order->tracking_number): ?>
                                                    <p class="mb-1">
                                                        <strong>Tracking:</strong><br>
                                                        <code><?php echo e($order->tracking_number); ?></code>
                                                    </p>
                                                <?php endif; ?>
                                                
                                                <div class="mt-3">
                                                    <a href="<?php echo e(route('customer.orders.show', $order)); ?>" 
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i> View Details
                                                    </a>
                                                    
                                                    <?php if($order->canBeTracked()): ?>
                                                        <a href="<?php echo e(route('customer.track')); ?>?tracking_number=<?php echo e($order->tracking_number); ?>" 
                                                           class="btn btn-outline-info btn-sm">
                                                            <i class="fas fa-map-marker-alt me-1"></i> Track
                                                        </a>
                                                    <?php endif; ?>
                                                    
                                                    <?php if($order->needsSupport()): ?>
                                                        <a href="<?php echo e(route('customer.support.create')); ?>?order_number=<?php echo e($order->order_number); ?>" 
                                                           class="btn btn-outline-warning btn-sm">
                                                            <i class="fas fa-headset me-1"></i> Support
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing <?php echo e($orders->firstItem()); ?> to <?php echo e($orders->lastItem()); ?> of <?php echo e($orders->total()); ?> orders
                    </div>
                    <?php echo e($orders->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No orders found</h5>
                    <p class="text-muted">You haven't placed any orders yet. Start shopping to see your orders here.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-shopping-cart me-2"></i> Start Shopping
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .stats-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.success {
        border-left-color: #28a745;
    }
    
    .stats-card.warning {
        border-left-color: #ffc107;
    }
    
    .stats-card.info {
        border-left-color: #17a2b8;
    }
    
    .stats-card.danger {
        border-left-color: #dc3545;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/customer/orders.blade.php ENDPATH**/ ?>