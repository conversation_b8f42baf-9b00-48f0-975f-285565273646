<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FixMissingLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->fixIreland();
        $this->fixPortugal();
        $this->fixGreece();
        $this->fixNetherlands();
        $this->fixBelgium();
        $this->fixSwitzerland();
        $this->fixAustria();
        $this->fixNordicCountries();
        $this->fixOtherEuropeanCountries();
    }

    private function fixIreland(): void
    {
        $ireland = Country::where('iso2', 'IE')->first();
        if (!$ireland) return;

        // Ireland has 4 provinces (traditional regions)
        $states = [
            [
                'name' => 'Leinster',
                'code' => 'L',
                'cities' => ['Dublin', 'Kilkenny', 'Wexford', 'Carlow', 'Kildare', 'Laois', 'Longford', 'Louth', 'Meath', 'Offaly', 'Westmeath', 'Wicklow']
            ],
            [
                'name' => 'Munster',
                'code' => 'M',
                'cities' => ['Cork', 'Waterford', 'Kerry', 'Limerick', 'Tipperary', 'Clare']
            ],
            [
                'name' => 'Connacht',
                'code' => 'C',
                'cities' => ['Galway', 'Mayo', 'Roscommon', 'Sligo', 'Leitrim']
            ],
            [
                'name' => 'Ulster',
                'code' => 'U',
                'cities' => ['Donegal', 'Cavan', 'Monaghan']
            ]
        ];

        $this->createStatesAndCities($ireland, $states);
    }

    private function fixPortugal(): void
    {
        $portugal = Country::where('iso2', 'PT')->first();
        if (!$portugal) return;

        $states = [
            [
                'name' => 'Lisbon',
                'code' => 'LIS',
                'cities' => ['Lisbon', 'Sintra', 'Cascais', 'Amadora', 'Oeiras', 'Almada', 'Setúbal', 'Barreiro']
            ],
            [
                'name' => 'Porto',
                'code' => 'POR',
                'cities' => ['Porto', 'Vila Nova de Gaia', 'Matosinhos', 'Gondomar', 'Maia', 'Valongo', 'Paredes', 'Penafiel']
            ],
            [
                'name' => 'Braga',
                'code' => 'BRA',
                'cities' => ['Braga', 'Guimarães', 'Famalicão', 'Barcelos', 'Esposende', 'Póvoa de Varzim', 'Vila do Conde']
            ],
            [
                'name' => 'Coimbra',
                'code' => 'COI',
                'cities' => ['Coimbra', 'Figueira da Foz', 'Cantanhede', 'Montemor-o-Velho', 'Soure', 'Condeixa-a-Nova']
            ]
        ];

        $this->createStatesAndCities($portugal, $states);
    }

    private function fixGreece(): void
    {
        $greece = Country::where('iso2', 'GR')->first();
        if (!$greece) return;

        $states = [
            [
                'name' => 'Attica',
                'code' => 'ATT',
                'cities' => ['Athens', 'Piraeus', 'Peristeri', 'Kallithea', 'Nikaia', 'Glyfada', 'Voula', 'Acharnes', 'Ilion', 'Keratsini']
            ],
            [
                'name' => 'Central Macedonia',
                'code' => 'MAC',
                'cities' => ['Thessaloniki', 'Serres', 'Katerini', 'Veria', 'Giannitsa', 'Kilkis', 'Edessa', 'Naoussa', 'Alexandria', 'Polygyros']
            ],
            [
                'name' => 'Crete',
                'code' => 'CRE',
                'cities' => ['Heraklion', 'Chania', 'Rethymno', 'Agios Nikolaos', 'Ierapetra', 'Sitia', 'Kissamos', 'Moires']
            ],
            [
                'name' => 'Western Greece',
                'code' => 'WGR',
                'cities' => ['Patras', 'Agrinio', 'Missolonghi', 'Pyrgos', 'Amaliada', 'Zakynthos', 'Argostoli', 'Lefkada']
            ]
        ];

        $this->createStatesAndCities($greece, $states);
    }

    private function fixNetherlands(): void
    {
        $netherlands = Country::where('iso2', 'NL')->first();
        if (!$netherlands) return;

        $states = [
            [
                'name' => 'North Holland',
                'code' => 'NH',
                'cities' => ['Amsterdam', 'Haarlem', 'Zaanstad', 'Haarlemmermeer', 'Alkmaar', 'Hilversum', 'Amstelveen', 'Purmerend', 'Hoorn', 'Velsen']
            ],
            [
                'name' => 'South Holland',
                'code' => 'ZH',
                'cities' => ['The Hague', 'Rotterdam', 'Leiden', 'Dordrecht', 'Zoetermeer', 'Delft', 'Alphen aan den Rijn', 'Westland', 'Gouda', 'Spijkenisse']
            ],
            [
                'name' => 'North Brabant',
                'code' => 'NB',
                'cities' => ['Eindhoven', 'Tilburg', 'Breda', 's-Hertogenbosch', 'Helmond', 'Oss', 'Bergen op Zoom', 'Roosendaal', 'Oosterhout', 'Uden']
            ],
            [
                'name' => 'Utrecht',
                'code' => 'UT',
                'cities' => ['Utrecht', 'Amersfoort', 'Nieuwegein', 'Veenendaal', 'Zeist', 'IJsselstein', 'Woerden', 'Houten', 'De Bilt', 'Bunnik']
            ]
        ];

        $this->createStatesAndCities($netherlands, $states);
    }

    private function fixBelgium(): void
    {
        $belgium = Country::where('iso2', 'BE')->first();
        if (!$belgium) return;

        $states = [
            [
                'name' => 'Brussels-Capital Region',
                'code' => 'BRU',
                'cities' => ['Brussels', 'Schaerbeek', 'Anderlecht', 'Molenbeek-Saint-Jean', 'Ixelles', 'Uccle', 'Forest', 'Saint-Gilles', 'Etterbeek', 'Evere']
            ],
            [
                'name' => 'Flanders',
                'code' => 'VLG',
                'cities' => ['Antwerp', 'Ghent', 'Bruges', 'Leuven', 'Mechelen', 'Aalst', 'Kortrijk', 'Hasselt', 'Sint-Niklaas', 'Ostend']
            ],
            [
                'name' => 'Wallonia',
                'code' => 'WAL',
                'cities' => ['Charleroi', 'Liège', 'Namur', 'Mons', 'La Louvière', 'Tournai', 'Verviers', 'Seraing', 'Mouscron', 'Arlon']
            ]
        ];

        $this->createStatesAndCities($belgium, $states);
    }

    private function fixSwitzerland(): void
    {
        $switzerland = Country::where('iso2', 'CH')->first();
        if (!$switzerland) return;

        $states = [
            [
                'name' => 'Zurich',
                'code' => 'ZH',
                'cities' => ['Zurich', 'Winterthur', 'Uster', 'Dübendorf', 'Dietikon', 'Wetzikon', 'Kloten', 'Schlieren', 'Vernier', 'Rapperswil-Jona']
            ],
            [
                'name' => 'Bern',
                'code' => 'BE',
                'cities' => ['Bern', 'Biel/Bienne', 'Thun', 'Köniz', 'Emmen', 'Steffisburg', 'Ostermundigen', 'Burgdorf', 'Langenthal', 'Worb']
            ],
            [
                'name' => 'Geneva',
                'code' => 'GE',
                'cities' => ['Geneva', 'Vernier', 'Lancy', 'Meyrin', 'Carouge', 'Onex', 'Thônex', 'Versoix', 'Grand-Saconnex', 'Plan-les-Ouates']
            ],
            [
                'name' => 'Basel-Stadt',
                'code' => 'BS',
                'cities' => ['Basel', 'Riehen', 'Bettingen']
            ]
        ];

        $this->createStatesAndCities($switzerland, $states);
    }

    private function fixAustria(): void
    {
        $austria = Country::where('iso2', 'AT')->first();
        if (!$austria) return;

        $states = [
            [
                'name' => 'Vienna',
                'code' => 'W',
                'cities' => ['Vienna']
            ],
            [
                'name' => 'Lower Austria',
                'code' => 'NÖ',
                'cities' => ['St. Pölten', 'Wiener Neustadt', 'Baden', 'Amstetten', 'Tulln', 'Krems', 'Mödling', 'Melk', 'Korneuburg', 'Stockerau']
            ],
            [
                'name' => 'Upper Austria',
                'code' => 'OÖ',
                'cities' => ['Linz', 'Wels', 'Steyr', 'Leonding', 'Traun', 'Ansfelden', 'Enns', 'Pasching', 'Vöcklabruck', 'Marchtrenk']
            ],
            [
                'name' => 'Styria',
                'code' => 'ST',
                'cities' => ['Graz', 'Leoben', 'Kapfenberg', 'Bruck an der Mur', 'Feldbach', 'Köflach', 'Voitsberg', 'Judenburg', 'Hartberg', 'Leibnitz']
            ]
        ];

        $this->createStatesAndCities($austria, $states);
    }

    private function fixNordicCountries(): void
    {
        // Sweden
        $sweden = Country::where('iso2', 'SE')->first();
        if ($sweden) {
            $states = [
                [
                    'name' => 'Stockholm County',
                    'code' => 'AB',
                    'cities' => ['Stockholm', 'Huddinge', 'Järfälla', 'Botkyrka', 'Haninge', 'Nacka', 'Sundbyberg', 'Solna', 'Sollentuna', 'Tyresö']
                ],
                [
                    'name' => 'Västra Götaland County',
                    'code' => 'O',
                    'cities' => ['Gothenburg', 'Borås', 'Mölndal', 'Trollhättan', 'Uddevalla', 'Skövde', 'Lidköping', 'Alingsås', 'Kungälv', 'Lerum']
                ],
                [
                    'name' => 'Skåne County',
                    'code' => 'M',
                    'cities' => ['Malmö', 'Helsingborg', 'Lund', 'Kristianstad', 'Landskrona', 'Trelleborg', 'Ängelholm', 'Hässleholm', 'Ystad', 'Eslöv']
                ]
            ];
            $this->createStatesAndCities($sweden, $states);
        }

        // Norway
        $norway = Country::where('iso2', 'NO')->first();
        if ($norway) {
            $states = [
                [
                    'name' => 'Oslo',
                    'code' => 'OS',
                    'cities' => ['Oslo']
                ],
                [
                    'name' => 'Viken',
                    'code' => 'VK',
                    'cities' => ['Bærum', 'Drammen', 'Asker', 'Lillestrøm', 'Sandvika', 'Ski', 'Moss', 'Sarpsborg', 'Fredrikstad', 'Halden']
                ],
                [
                    'name' => 'Vestland',
                    'code' => 'VL',
                    'cities' => ['Bergen', 'Stavanger', 'Haugesund', 'Ålesund', 'Molde', 'Florø', 'Førde', 'Odda', 'Voss', 'Stord']
                ]
            ];
            $this->createStatesAndCities($norway, $states);
        }

        // Denmark
        $denmark = Country::where('iso2', 'DK')->first();
        if ($denmark) {
            $states = [
                [
                    'name' => 'Capital Region',
                    'code' => 'H',
                    'cities' => ['Copenhagen', 'Frederiksberg', 'Gentofte', 'Gladsaxe', 'Lyngby-Taarbæk', 'Herlev', 'Rødovre', 'Albertslund', 'Hvidovre', 'Ballerup']
                ],
                [
                    'name' => 'Central Denmark Region',
                    'code' => 'M',
                    'cities' => ['Aarhus', 'Aalborg', 'Randers', 'Kolding', 'Horsens', 'Vejle', 'Silkeborg', 'Herning', 'Holstebro', 'Fredericia']
                ],
                [
                    'name' => 'Region of Southern Denmark',
                    'code' => 'S',
                    'cities' => ['Odense', 'Esbjerg', 'Kolding', 'Vejle', 'Fredericia', 'Haderslev', 'Aabenraa', 'Sønderborg', 'Tønder', 'Varde']
                ]
            ];
            $this->createStatesAndCities($denmark, $states);
        }

        // Finland
        $finland = Country::where('iso2', 'FI')->first();
        if ($finland) {
            $states = [
                [
                    'name' => 'Uusimaa',
                    'code' => 'UU',
                    'cities' => ['Helsinki', 'Espoo', 'Vantaa', 'Kauniainen', 'Hyvinkää', 'Järvenpää', 'Kerava', 'Kirkkonummi', 'Nurmijärvi', 'Sipoo']
                ],
                [
                    'name' => 'Pirkanmaa',
                    'code' => 'PI',
                    'cities' => ['Tampere', 'Nokia', 'Ylöjärvi', 'Kangasala', 'Orivesi', 'Ikaalinen', 'Mänttä-Vilppula', 'Parkano', 'Valkeakoski', 'Lempäälä']
                ],
                [
                    'name' => 'Southwest Finland',
                    'code' => 'VS',
                    'cities' => ['Turku', 'Kaarina', 'Raisio', 'Naantali', 'Salo', 'Loimaa', 'Somero', 'Paimio', 'Lieto', 'Mynämäki']
                ]
            ];
            $this->createStatesAndCities($finland, $states);
        }
    }

    private function fixOtherEuropeanCountries(): void
    {
        // Add default states for countries that don't have administrative divisions
        $countriesNeedingDefaultStates = [
            'IS' => 'Iceland',
            'LU' => 'Luxembourg', 
            'MT' => 'Malta',
            'CY' => 'Cyprus',
            'EE' => 'Estonia',
            'LV' => 'Latvia',
            'LT' => 'Lithuania',
            'SI' => 'Slovenia',
            'SK' => 'Slovakia',
            'HR' => 'Croatia',
            'BG' => 'Bulgaria',
            'RO' => 'Romania',
            'HU' => 'Hungary',
            'CZ' => 'Czech Republic',
            'PL' => 'Poland'
        ];

        foreach ($countriesNeedingDefaultStates as $iso2 => $countryName) {
            $country = Country::where('iso2', $iso2)->first();
            if ($country && $country->states()->count() === 0) {
                // Create a default state with the country name
                $state = State::create([
                    'country_id' => $country->id,
                    'name' => $country->name,
                    'code' => 'DEFAULT',
                    'is_active' => true,
                    'sort_order' => 1
                ]);

                // Add some major cities (this would be expanded with real data)
                $majorCities = $this->getMajorCitiesForCountry($iso2);
                foreach ($majorCities as $index => $cityName) {
                    City::create([
                        'state_id' => $state->id,
                        'name' => $cityName,
                        'is_active' => true,
                        'sort_order' => $index + 1
                    ]);
                }
            }
        }
    }

    private function getMajorCitiesForCountry(string $iso2): array
    {
        $cities = [
            'IS' => ['Reykjavik', 'Kópavogur', 'Hafnarfjörður', 'Akureyri', 'Garðabær'],
            'LU' => ['Luxembourg City', 'Esch-sur-Alzette', 'Differdange', 'Dudelange', 'Ettelbruck'],
            'MT' => ['Valletta', 'Birkirkara', 'Mosta', 'Qormi', 'Żabbar'],
            'CY' => ['Nicosia', 'Limassol', 'Larnaca', 'Famagusta', 'Paphos'],
            'EE' => ['Tallinn', 'Tartu', 'Narva', 'Pärnu', 'Kohtla-Järve'],
            'LV' => ['Riga', 'Daugavpils', 'Liepāja', 'Jelgava', 'Jūrmala'],
            'LT' => ['Vilnius', 'Kaunas', 'Klaipėda', 'Šiauliai', 'Panevėžys'],
            'SI' => ['Ljubljana', 'Maribor', 'Celje', 'Kranj', 'Velenje'],
            'SK' => ['Bratislava', 'Košice', 'Prešov', 'Žilina', 'Banská Bystrica'],
            'HR' => ['Zagreb', 'Split', 'Rijeka', 'Osijek', 'Zadar'],
            'BG' => ['Sofia', 'Plovdiv', 'Varna', 'Burgas', 'Ruse'],
            'RO' => ['Bucharest', 'Cluj-Napoca', 'Timișoara', 'Iași', 'Constanța'],
            'HU' => ['Budapest', 'Debrecen', 'Szeged', 'Miskolc', 'Pécs'],
            'CZ' => ['Prague', 'Brno', 'Ostrava', 'Plzen', 'Liberec'],
            'PL' => ['Warsaw', 'Kraków', 'Łódź', 'Wrocław', 'Poznań']
        ];

        return $cities[$iso2] ?? [];
    }

    private function createStatesAndCities(Country $country, array $states): void
    {
        foreach ($states as $index => $stateData) {
            $cities = $stateData['cities'] ?? [];
            unset($stateData['cities']);

            $state = State::firstOrCreate(
                [
                    'country_id' => $country->id,
                    'code' => $stateData['code']
                ],
                array_merge($stateData, [
                    'country_id' => $country->id,
                    'is_active' => true,
                    'sort_order' => $index + 1
                ])
            );

            foreach ($cities as $cityIndex => $cityName) {
                City::firstOrCreate(
                    [
                        'state_id' => $state->id,
                        'name' => $cityName
                    ],
                    [
                        'state_id' => $state->id,
                        'name' => $cityName,
                        'is_active' => true,
                        'sort_order' => $cityIndex + 1
                    ]
                );
            }
        }
    }
}
