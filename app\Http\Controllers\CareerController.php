<?php

namespace App\Http\Controllers;

use App\Models\Career;
use App\Models\JobApplication;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\JobApplicationSubmitted;

class CareerController extends Controller
{
    /**
     * Display a listing of careers
     */
    public function index(): View
    {
        $careers = Career::active()
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $departments = Career::active()
            ->whereNotNull('department')
            ->distinct()
            ->pluck('department')
            ->sort();

        return view('careers.index', compact('careers', 'departments'));
    }

    /**
     * Display the specified career
     */
    public function show(Career $career): View
    {
        if (!$career->is_active) {
            abort(404);
        }

        return view('careers.show', compact('career'));
    }

    /**
     * Show the job application form
     */
    public function apply(Career $career): View
    {
        if (!$career->is_active || $career->isApplicationDeadlinePassed()) {
            abort(404);
        }

        return view('careers.apply', compact('career'));
    }

    /**
     * Store a job application
     */
    public function storeApplication(Request $request, Career $career)
    {
        if (!$career->is_active || $career->isApplicationDeadlinePassed()) {
            abort(404);
        }

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'nullable|string|max:500',
            'country' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'city' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'cover_letter' => 'nullable|string|max:2000',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'years_of_experience' => 'nullable|integer|min:0|max:50',
            'expected_salary' => 'nullable|numeric|min:0',
            'availability' => 'nullable|string|max:255',
            'willing_to_relocate' => 'nullable|boolean',
            'skills' => 'nullable|array',
            'skills.*' => 'string|max:255',
            'why_interested' => 'nullable|string|max:1000',
            'referral_source' => 'nullable|string|max:255',
            'consent_data_processing' => 'required|accepted',
        ]);

        $validated['career_id'] = $career->id;
        $validated['willing_to_relocate'] = $request->has('willing_to_relocate');

        // Location names are already provided directly from the form
        // No need to convert from IDs since Tom Select sends names directly

        try {
            // Handle resume upload
            if ($request->hasFile('resume')) {
                $validated['resume_path'] = $request->file('resume')->store('uploads/resumes', 'public');
            }

            $application = JobApplication::create($validated);

            // Send confirmation email to applicant
            try {
                Mail::to($application->email)->send(new JobApplicationSubmitted($application));
            } catch (\Exception $e) {
                Log::error('Failed to send job application confirmation email', [
                    'application_id' => $application->id,
                    'email' => $application->email,
                    'error' => $e->getMessage()
                ]);
            }

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Your application has been submitted successfully! We will review it and get back to you soon.',
                    'reference_number' => $application->reference_number,
                    'applicant_name' => $application->full_name
                ]);
            }

            return redirect()->route('careers.index')
                            ->with('success', 'Your application has been submitted successfully!')
                            ->with('reference_number', $application->reference_number)
                            ->with('applicant_name', $application->full_name)
                            ->with('application_submitted', true);
        } catch (\Exception $e) {
            Log::error('Job application submission failed', [
                'career_id' => $career->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while submitting your application. Please try again.'
                ], 500);
            }

            return redirect()->back()
                            ->withInput()
                            ->with('error', 'An error occurred while submitting your application. Please try again.');
        }
    }

    /**
     * Filter careers by department
     */
    public function filterByDepartment(Request $request): View
    {
        $department = $request->get('department');

        $careers = Career::active()
            ->when($department, function($query, $department) {
                return $query->where('department', $department);
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $departments = Career::active()
            ->whereNotNull('department')
            ->distinct()
            ->pluck('department')
            ->sort();

        return view('careers.index', compact('careers', 'departments', 'department'));
    }
}
