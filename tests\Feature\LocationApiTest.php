<?php

namespace Tests\Feature;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LocationApiTest extends TestCase
{
    // Don't use RefreshDatabase for this test since we want to test with actual data

    // Test with actual seeded data instead of creating test data

    public function test_can_get_all_countries(): void
    {
        $response = $this->get('/api/locations/countries');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'code',
                            'iso2',
                            'phone_code',
                            'currency'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(100, count($data)); // Should have 144 countries

        $countryNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('United States', $countryNames);
        $this->assertContains('Ireland', $countryNames);
        $this->assertContains('Germany', $countryNames);
        $this->assertContains('France', $countryNames);
    }

    public function test_can_get_states_for_country(): void
    {
        $usa = Country::where('iso2', 'US')->first();
        $this->assertNotNull($usa, 'United States should exist in database');

        $response = $this->get("/api/locations/states/{$usa->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'code'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data));

        $stateNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('California', $stateNames);
    }

    public function test_can_get_states_for_ireland(): void
    {
        $ireland = Country::where('iso2', 'IE')->first();
        $this->assertNotNull($ireland, 'Ireland should exist in database');

        $response = $this->get("/api/locations/states/{$ireland->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data), 'Ireland should have states');

        $stateNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Leinster', $stateNames);
        $this->assertContains('Munster', $stateNames);
        $this->assertContains('Connacht', $stateNames);
        $this->assertContains('Ulster', $stateNames);
    }

    public function test_can_get_cities_for_state(): void
    {
        $california = State::where('code', 'CA')->first();
        $this->assertNotNull($california, 'California should exist in database');

        $response = $this->get("/api/locations/cities/{$california->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name'
                        ]
                    ]
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data));

        $cityNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Los Angeles', $cityNames);
        $this->assertContains('San Francisco', $cityNames);
    }

    public function test_can_get_cities_for_ireland(): void
    {
        $ireland = Country::where('iso2', 'IE')->first();
        $this->assertNotNull($ireland, 'Ireland should exist in database');

        $leinster = $ireland->states()->where('name', 'Leinster')->first();
        $this->assertNotNull($leinster, 'Leinster should exist as a state in Ireland');

        $response = $this->get("/api/locations/cities/{$leinster->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data));

        $cityNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('Dublin', $cityNames);
        $this->assertContains('Kilkenny', $cityNames);
        $this->assertContains('Wexford', $cityNames);
    }

    public function test_returns_empty_for_nonexistent_country(): void
    {
        $response = $this->get('/api/locations/states/999');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => []
                ]);
    }

    public function test_returns_empty_for_nonexistent_state(): void
    {
        $response = $this->get('/api/locations/cities/999');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => []
                ]);
    }

    public function test_location_search_functionality(): void
    {
        $response = $this->get('/api/locations/search?q=United&type=country');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data));
        
        $foundCountry = collect($data)->firstWhere('name', 'United States');
        $this->assertNotNull($foundCountry);
        $this->assertEquals('country', $foundCountry['type']);
    }

    public function test_location_search_cities(): void
    {
        $response = $this->get('/api/locations/search?q=Los&type=city');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, count($data));
        
        $foundCity = collect($data)->firstWhere('name', 'Los Angeles');
        $this->assertNotNull($foundCity);
        $this->assertEquals('city', $foundCity['type']);
    }
}
