<?php $__env->startSection('title', 'Access Forbidden - 403'); ?>
<?php $__env->startSection('description', 'You do not have permission to access this resource. Please contact support if you believe this is an error.'); ?>

<?php $__env->startSection('content'); ?>
<!-- 403 Error Page -->
<section class="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50 flex items-center justify-center py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- 403 Animation -->
            <div class="mb-12">
                <div class="relative inline-block">
                    <!-- Large 403 Text -->
                    <h1 class="text-9xl md:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 via-orange-600 to-red-800 leading-none select-none">
                        403
                    </h1>
                    
                    <!-- Floating Elements -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="relative">
                            <!-- Lock Icon -->
                            <div class="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center animate-bounce">
                                <i class="fas fa-lock text-4xl text-orange-600"></i>
                            </div>
                            
                            <!-- Floating Particles -->
                            <div class="absolute -top-4 -right-4 w-3 h-3 bg-yellow-500 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-orange-500 rounded-full animate-ping animation-delay-300"></div>
                            <div class="absolute top-1/2 -right-8 w-1.5 h-1.5 bg-red-500 rounded-full animate-ping animation-delay-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Access Forbidden
                </h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    You don't have permission to access this resource. 
                    This area is restricted and requires special authorization.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <a href="<?php echo e(route('home')); ?>" 
                   class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-600 to-red-600 text-white font-semibold rounded-full hover:from-orange-700 hover:to-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-home mr-3"></i>
                    Return Home
                </a>
                
                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('dashboard')); ?>" 
                       class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-orange-500 hover:text-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Go to Dashboard
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" 
                       class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-orange-500 hover:text-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-sign-in-alt mr-3"></i>
                        Login
                    </a>
                <?php endif; ?>
            </div>

            <!-- Quick Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Login -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-user-circle text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Account Access</h3>
                    <p class="text-gray-600 mb-6">Login to your account to access restricted areas</p>
                    <?php if(auth()->guard()->check()): ?>
                        <span class="inline-flex items-center text-green-600 font-semibold">
                            <i class="fas fa-check-circle mr-2"></i>
                            Logged in as <?php echo e(auth()->user()->name); ?>

                        </span>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" 
                           class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                            Login Now <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Request Access -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-key text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Request Access</h3>
                    <p class="text-gray-600 mb-6">Need access to this area? Contact our support team</p>
                    <a href="<?php echo e(route('contact')); ?>" 
                       class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                        Request Access <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Help Center -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-question-circle text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Need Help?</h3>
                    <p class="text-gray-600 mb-6">Get assistance with access permissions</p>
                    <a href="<?php echo e(route('contact')); ?>" 
                       class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors">
                        Get Help <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Need Access Authorization?</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Phone -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-phone text-orange-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Call Support</h4>
                        <p class="text-gray-600">
                            <a href="tel:<?php echo e($siteSettings['contact_phone'] ?? '+****************'); ?>" 
                               class="hover:text-orange-600 transition-colors">
                                <?php echo e($siteSettings['contact_phone'] ?? '+****************'); ?>

                            </a>
                        </p>
                    </div>

                    <!-- Email -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-envelope text-red-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Email Support</h4>
                        <p class="text-gray-600">
                            <a href="mailto:<?php echo e($siteSettings['support_email'] ?? '<EMAIL>'); ?>" 
                               class="hover:text-red-600 transition-colors">
                                <?php echo e($siteSettings['support_email'] ?? '<EMAIL>'); ?>

                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .animation-delay-300 {
        animation-delay: 300ms;
    }
    
    .animation-delay-600 {
        animation-delay: 600ms;
    }
</style>

<script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.transform');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/errors/403.blade.php ENDPATH**/ ?>