<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        body { font-family: 'DejaVu Sans', sans-serif; margin: 0; padding: 0; color: #333; font-size: 12px; }
        .container { width: 100%; margin: 0 auto; padding: 20px; }
        .header, .footer { text-align: center; }
        .header h1 { margin: 0; font-size: 24px; color: #{{ $siteSettings['theme_color_primary'] ?? '3498db' }}; }
        .header p { margin: 5px 0; }
        .company-logo { max-height: 80px; margin-bottom: 15px; }
        .invoice-details, .billing-details { margin-bottom: 30px; }
        .invoice-details table, .billing-details table { width: 100%; }
        .invoice-details td, .billing-details td { padding: 5px; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .items-table th { background-color: #f9f9f9; font-weight: bold; }
        .items-table .text-right { text-align: right; }
        .totals-table { width: 40%; float: right; margin-bottom: 30px; }
        .totals-table td { padding: 5px; }
        .totals-table .strong { font-weight: bold; }
        .notes, .payment-instructions { margin-bottom: 30px; padding: 10px; border: 1px solid #eee; border-radius: 4px; }
        .footer { position: fixed; bottom: 0; left: 0; right: 0; font-size: 10px; color: #777; border-top: 1px solid #eee; padding-top: 10px; }
        .clearfix::after { content: ""; clear: both; display: table; }
        .text-primary { color: #{{ $siteSettings['theme_color_primary'] ?? '3498db' }}; }
        .page-break { page-break-after: always; }
    </style>
</head>
<body>
    <div class="container">
        <table style="width: 100%; margin-bottom: 20px;">
            <tr>
                <td style="width: 60%;">
                    @if(isset($siteSettings['site_logo_invoice']) && $siteSettings['site_logo_invoice'] && file_exists(Storage::path($siteSettings['site_logo_invoice'])))
                        <img src="{{ Storage::path($siteSettings['site_logo_invoice']) }}" alt="Logo" class="company-logo">
                    @elseif(isset($siteSettings['site_logo']) && $siteSettings['site_logo'] && file_exists(Storage::path($siteSettings['site_logo'])))
                        <img src="{{ Storage::path($siteSettings['site_logo']) }}" alt="Logo" class="company-logo">
                    @else
                        <h1 class="text-primary">{{ $siteSettings['site_name'] ?? 'Your Company' }}</h1>
                    @endif
                    <p>
                        {!! nl2br(e($siteSettings['company_address'] ?? '123 Business Rd, City, Country')) !!}<br>
                        @if(isset($siteSettings['company_email'])) Email: {{ $siteSettings['company_email'] }}<br> @endif
                        @if(isset($siteSettings['company_phone'])) Phone: {{ $siteSettings['company_phone'] }}<br> @endif
                        @if(isset($siteSettings['company_vat_number'])) VAT: {{ $siteSettings['company_vat_number'] }} @endif
                    </p>
                </td>
                <td style="width: 40%; text-align: right; vertical-align: top;">
                    <h2 style="font-size: 28px; margin:0; color: #555;">INVOICE</h2>
                    <p style="margin:0;"><strong>Invoice #:</strong> {{ $invoice->invoice_number }}</p>
                    <p style="margin:0;"><strong>Date:</strong> {{ $invoice->issue_date->format('M d, Y') }}</p>
                    @if($invoice->due_date)
                    <p style="margin:0;"><strong>Due Date:</strong> {{ $invoice->due_date->format('M d, Y') }}</p>
                    @endif
                    <p style="margin:0;"><strong>Parcel #:</strong> {{ $invoice->parcel->tracking_number }}</p>
                </td>
            </tr>
        </table>

        <div class="billing-details" style="margin-bottom: 30px;">
            <table style="width: 50%;">
                <tr><td><strong class="text-primary">BILL TO:</strong></td></tr>
                <tr><td><strong>{{ $invoice->issued_to_name }}</strong></td></tr>
                @if($invoice->issued_to_address)
                <tr><td>{!! nl2br(e($invoice->issued_to_address)) !!}</td></tr>
                @endif
                @if($invoice->issued_to_email)
                <tr><td>{{ $invoice->issued_to_email }}</td></tr>
                @endif
            </table>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Description</th>
                    <th class="text-right">Quantity</th>
                    <th class="text-right">Unit Price</th>
                    <th class="text-right">Amount</th>
                </tr>
            </thead>
            <tbody>
                @foreach($invoice->items as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $item->description }}</td>
                    <td class="text-right">{{ number_format($item->quantity, 2) }}</td>
                    <td class="text-right">{{ \App\Helpers\CurrencyHelper::format($item->unit_price, true, $invoice->parcel->currency_code ?? null) }}</td>
                    <td class="text-right">{{ \App\Helpers\CurrencyHelper::format($item->amount, true, $invoice->parcel->currency_code ?? null) }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <div class="clearfix">
            <table class="totals-table">
                <tr>
                    <td>Subtotal:</td>
                    <td class="text-right">{{ \App\Helpers\CurrencyHelper::format($invoice->sub_total, true, $invoice->parcel->currency_code ?? null) }}</td>
                </tr>
                @if($invoice->tax_amount > 0)
                <tr>
                    <td>Tax:</td>
                    <td class="text-right">{{ \App\Helpers\CurrencyHelper::format($invoice->tax_amount, true, $invoice->parcel->currency_code ?? null) }}</td>
                </tr>
                @endif
                <tr>
                    <td class="strong text-primary">Total:</td>
                    <td class="text-right strong text-primary" style="font-size: 16px;">{{ \App\Helpers\CurrencyHelper::format($invoice->total_amount, true, $invoice->parcel->currency_code ?? null) }}</td>
                </tr>
            </table>
        </div>

        @if($invoice->notes)
        <div class="notes">
            <strong>Notes:</strong>
            <p>{{ $invoice->notes }}</p>
        </div>
        @endif

        @if($invoice->payment_instructions)
        <div class="payment-instructions">
            <strong>Payment Instructions:</strong>
            <p>{!! nl2br(e($invoice->payment_instructions)) !!}</p>
        </div>
        @endif

        <div class="footer">
            <p>Thank you for your business!</p>
            <p>{{ $siteSettings['site_name'] ?? 'Your Company' }} | {{ url('/') }}</p>
        </div>
    </div>
</body>
</html>
