@extends('layouts.admin')

@section('title', 'Invoice ' . $invoice->invoice_number)
@section('page-title', 'Invoice Details')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.invoices.download', $invoice) }}" class="btn btn-primary">
            <i class="fas fa-download me-1"></i> Download PDF
        </a>
        @if($invoice->status === 'draft' || $invoice->status === 'sent')
            {{-- Add Mark as Paid button here --}}
        @endif
        <a href="{{ route('admin.invoices.index.all') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Invoices
        </a>
    </div>
@endsection

@section('content')
<div class="card">
    <div class="card-body">
        {{-- Basic Invoice Preview - For a full preview, you might include the PDF template's HTML here --}}
        <div class="row mb-4">
            <div class="col-md-6">
                <h4>Invoice #{{ $invoice->invoice_number }}</h4>
                <p>Parcel: <a href="{{ route('admin.parcels.show', $invoice->parcel) }}">{{ $invoice->parcel->tracking_number }}</a></p>
            </div>
            <div class="col-md-6 text-md-end">
                <p><strong>Issue Date:</strong> {{ $invoice->issue_date->format('M d, Y') }}</p>
                <p><strong>Due Date:</strong> {{ $invoice->due_date ? $invoice->due_date->format('M d, Y') : 'N/A' }}</p>
                <p><strong>Status:</strong> <span class="badge bg-info">{{ ucfirst($invoice->status) }}</span></p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <h6>Bill To:</h6>
                <address>
                    <strong>{{ $invoice->issued_to_name }}</strong><br>
                    @if($invoice->issued_to_address)
                        {!! nl2br(e($invoice->issued_to_address)) !!}<br>
                    @endif
                    @if($invoice->issued_to_email)
                        {{ $invoice->issued_to_email }}
                    @endif
                </address>
            </div>
            <div class="col-md-6 text-md-end">
                @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                    <img src="{{ Storage::url($siteSettings['site_logo']) }}" alt="{{ $siteSettings['site_name'] ?? 'Company Logo' }}" style="max-height: 70px;" class="mb-2">
                @endif
                <h6>{{ $siteSettings['site_name'] ?? 'Your Company' }}</h6>
                <address>
                    {!! nl2br(e($siteSettings['company_address'] ?? 'Your Company Address')) !!}<br>
                    {{ $siteSettings['company_email'] ?? '' }}<br>
                    {{ $siteSettings['company_phone'] ?? '' }}
                </address>
            </div>
        </div>

        <h6>Items:</h6>
        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-end">Quantity</th>
                        <th class="text-end">Unit Price</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($invoice->items as $item)
                    <tr>
                        <td>{{ $item->description }}</td>
                        <td class="text-end">{{ number_format($item->quantity, 2) }}</td>
                        <td class="text-end">@currency($item->unit_price)</td>
                        <td class="text-end">@currency($item->amount)</td>
                    </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3" class="text-end"><strong>Subtotal:</strong></td>
                        <td class="text-end">@currency($invoice->sub_total)</td>
                    </tr>
                    @if($invoice->tax_amount > 0)
                    <tr>
                        <td colspan="3" class="text-end"><strong>Tax:</strong></td>
                        <td class="text-end">@currency($invoice->tax_amount)</td>
                    </tr>
                    @endif
                    <tr>
                        <td colspan="3" class="text-end"><strong>Total:</strong></td>
                        <td class="text-end fw-bold fs-5">@currency($invoice->total_amount)</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        @if($invoice->notes)
            <h6>Notes:</h6>
            <p>{{ $invoice->notes }}</p>
        @endif

        @if($invoice->payment_instructions)
            <h6>Payment Instructions:</h6>
            <p>{!! nl2br(e($invoice->payment_instructions)) !!}</p>
        @endif
    </div>
</div>
@endsection
