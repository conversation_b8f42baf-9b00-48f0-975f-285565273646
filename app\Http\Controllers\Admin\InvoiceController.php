<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Parcel;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function indexAll(Request $request)
    {
        $invoices = Invoice::with('parcel')->latest()->paginate(15);
        return view('admin.invoices.index', compact('invoices'));
    }
    

    /**
     * Show the form for creating a new resource.
     */
    public function create(Parcel $parcel)
    {
        return view('admin.invoices.create', compact('parcel'));
    }
    

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'parcel_id' => 'required|exists:parcels,id',
            'invoice_number' => 'required|string|unique:invoices,invoice_number',
            'issued_to_type' => 'required|string|in:sender,recipient,other',
            'issued_to_name' => 'required|string|max:255',
            'issued_to_email' => 'nullable|email|max:255',
            'issued_to_address' => 'nullable|string',
            'issue_date' => 'required|date',
            'due_date' => 'nullable|date|after_or_equal:issue_date',
            'status' => 'required|string|in:draft,sent,paid',
            'notes' => 'nullable|string',
            'payment_instructions' => 'nullable|string',
            'sub_total' => 'required|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.description' => 'required|string|max:255',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            $invoice = Invoice::create([
                'parcel_id' => $validated['parcel_id'],
                'invoice_number' => $validated['invoice_number'],
                'issued_to_type' => $validated['issued_to_type'],
                'issued_to_name' => $validated['issued_to_name'],
                'issued_to_email' => $validated['issued_to_email'],
                'issued_to_address' => $validated['issued_to_address'],
                'issue_date' => $validated['issue_date'],
                'due_date' => $validated['due_date'],
                'status' => $validated['status'],
                'notes' => $validated['notes'],
                'payment_instructions' => $validated['payment_instructions'],
                'sub_total' => $validated['sub_total'],
                'tax_amount' => $validated['tax_amount'],
                'total_amount' => $validated['total_amount'],
                'created_by_id' => Auth::id(),
            ]);

            foreach ($validated['items'] as $itemData) {
                $invoice->items()->create([
                    'description' => $itemData['description'],
                    'quantity' => $itemData['quantity'],
                    'unit_price' => $itemData['unit_price'],
                    'amount' => $itemData['quantity'] * $itemData['unit_price'],
                ]);
            }

            DB::commit();
            return redirect()->route('admin.invoices.show', $invoice)->with('success', 'Invoice created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()->with('error', 'Failed to create invoice: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['parcel', 'items', 'createdBy']);
        $siteSettings = SiteSetting::getByGroup('general');
        return view('admin.invoices.show', compact('invoice', 'siteSettings'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Invoice $invoice)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        if ($invoice->status !== 'draft') {
            return redirect()->back()->with('error', 'Only draft invoices can be deleted.');
        }
        $invoice->is_deleted = true;
        $invoice->deleted_by_id = Auth::id();
        $invoice->save();
        return redirect()->route('admin.invoices.index')->with('success', 'Invoice deleted successfully.');
    }

    /**
     * Generate PDF for the invoice.
     */
    public function downloadPdf(Invoice $invoice)
    {
        $invoice->load(['parcel', 'items', 'createdBy']);
        if (!$invoice) {
            // This check is likely redundant due to route model binding, but kept for safety
            return redirect()->back()->with('error', 'Invoice not found.');
        }
        if ($invoice->is_deleted) {
            return redirect()->back()->with('error', 'This invoice has been deleted.');
        }
        $siteSettings['site_name'] = SiteSetting::getValue('site_name', 'My Parcel Service');
        $siteSettings['site_email'] = SiteSetting::getValue('site_email', '');
        $siteSettings['company_phone'] = SiteSetting::getValue('company_phone', ''); // Corrected key name based on PDF template
        $siteSettings['company_address'] = SiteSetting::getValue('company_address', ''); // Corrected key name based on PDF template
        $siteSettings['site_logo'] = SiteSetting::getValue('site_logo', 'default-logo.png');
        $siteSettings['theme_color_primary'] = SiteSetting::getValue('theme_color', '#007bff');
        $siteSettings['site_logo_invoice'] = SiteSetting::getValue('site_logo_invoice'); // Ensure invoice logo is fetched

        // Ensure the invoice has items
        config(['dompdf.options.default_font' => 'DejaVu Sans']);

        $pdf = Pdf::loadView('admin.invoices.pdf.default', compact('invoice', 'siteSettings'));
        return $pdf->download("invoice-{$invoice->invoice_number}.pdf");

    }
}
