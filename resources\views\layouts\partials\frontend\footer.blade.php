<!-- Footer -->
<footer class="bg-gray-900 text-white">
    <!-- Main Footer Content -->
    <div class="container mx-auto px-4 py-16">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-12 gap-8">
            <!-- Company Info -->
            <div class="lg:col-span-4 space-y-6">
                <div class="flex items-center">
                    @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                        <img src="{{ Storage::url($siteSettings['site_logo']) }}" alt="{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}" class="h-10 w-auto dark:filter dark:brightness-0 dark:invert">
                    @else
                        <div class="flex items-center">
                            <div class="bg-green-600 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-shipping-fast text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold">{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</h3>
                                @if(isset($siteSettings['site_tagline']))
                                <p class="text-xs text-gray-400">{{ $siteSettings['site_tagline'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>

                <p class="text-gray-300 leading-relaxed">
                    {{ $siteSettings['company_description'] ?? 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.' }}
                </p>

                <!-- Contact Info -->
                <div class="space-y-3">
                    @if(isset($siteSettings['contact_address']))
                    <div class="flex items-start">
                        <i class="fas fa-map-marker-alt text-green-500 mt-1 mr-3"></i>
                        <span class="text-gray-300">{{ $siteSettings['contact_address'] }}</span>
                    </div>
                    @endif

                    @if(isset($siteSettings['contact_phone']))
                    <div class="flex items-center">
                        <i class="fas fa-phone text-green-500 mr-3"></i>
                        <a href="tel:{{ $siteSettings['contact_phone'] }}" class="text-gray-300 hover:text-green-400 transition-colors">
                            {{ $siteSettings['contact_phone'] }}
                        </a>
                    </div>
                    @endif

                    @if(isset($siteSettings['contact_email']))
                    <div class="flex items-center">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:{{ $siteSettings['contact_email'] }}" class="text-gray-300 hover:text-green-400 transition-colors">
                            {{ $siteSettings['contact_email'] }}
                        </a>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Links -->
            <div class="lg:col-span-2 space-y-6">
                <h3 class="text-lg font-semibold text-white">Quick Links</h3>
                <ul class="space-y-3">
                    <li><a href="{{ route('home') }}" class="text-gray-300 hover:text-green-400 transition-colors">Home</a></li>
                    <li><a href="{{ route('about') }}" class="text-gray-300 hover:text-green-400 transition-colors">About Us</a></li>
                    <li><a href="{{ route('products.index') }}" class="text-gray-300 hover:text-green-400 transition-colors">Products</a></li>
                    <li><a href="{{ route('tracking.index') }}" class="text-gray-300 hover:text-green-400 transition-colors">Track Shipment</a></li>
                    <li><a href="{{ route('blog.index') }}" class="text-gray-300 hover:text-green-400 transition-colors">Blog</a></li>
                    <li><a href="{{ route('careers.index') }}" class="text-gray-300 hover:text-green-400 transition-colors">Careers</a></li>
                    <li><a href="{{ route('contact') }}" class="text-gray-300 hover:text-green-400 transition-colors">Contact</a></li>
                </ul>
            </div>

            <!-- Services -->
            <div class="lg:col-span-2 space-y-6">
                <h3 class="text-lg font-semibold text-white">Our Services</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-truck text-green-500 mr-3"></i>
                            Freight & Logistics
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-warehouse text-green-500 mr-3"></i>
                            Warehousing
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-globe text-green-500 mr-3"></i>
                            Global Shipping
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-shield-alt text-green-500 mr-3"></i>
                            Supply Chain
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-plane text-green-500 mr-3"></i>
                            Air Freight
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('services') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-bolt text-green-500 mr-3"></i>
                            Express Delivery
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Products Categories -->
            <div class="lg:col-span-2 space-y-6">
                <h3 class="text-lg font-semibold text-white">Products for Sale</h3>
                <ul class="space-y-3">
                    @php
                        $footerCategories = \App\Models\Category::footerFeatured()
                            ->orderBy('sort_order')
                            ->orderBy('name')
                            ->limit(6)
                            ->get();
                    @endphp

                    @forelse($footerCategories as $category)
                        <li>
                            <a href="{{ route('categories.show', $category->slug) }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                @if($category->footer_icon)
                                    <i class="{{ $category->footer_icon }} mr-3"></i>
                                @elseif($category->icon)
                                    <i class="{{ $category->icon }} mr-3"></i>
                                @else
                                    <i class="fas fa-cube text-blue-500 mr-3"></i>
                                @endif
                                {{ $category->footer_text ?: $category->name }}
                            </a>
                        </li>
                    @empty
                        <li>
                            <a href="{{ route('categories.show', 'shipping-containers') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-cube text-blue-500 mr-3"></i>
                                Shipping Containers
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('categories.show', 'cardboard-boxes') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-box text-orange-500 mr-3"></i>
                                CardBoxes
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('categories.show', 'steel-products') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-industry text-gray-500 mr-3"></i>
                                Steel Products
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('categories.show', 'spare-parts') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-cog text-purple-500 mr-3"></i>
                                Spare Parts
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('categories.show', 'packaging-materials') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-tape text-yellow-500 mr-3"></i>
                                Packaging Materials
                            </a>
                        </li>
                    @endforelse

                    <li>
                        <a href="{{ route('products.index') }}" class="flex items-center text-gray-300 hover:text-green-400 transition-colors font-semibold">
                            <i class="fas fa-th-large text-green-500 mr-3"></i>
                            Browse our Inventory
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Newsletter & Social -->
            <div class="lg:col-span-2 space-y-6">
                <h3 class="text-lg font-semibold text-white">Stay Connected</h3>
                <p class="text-secondary-300">Subscribe to our newsletter for updates and industry insights.</p>

                <!-- Newsletter Form -->
                <form action="{{ route('newsletter.subscribe') }}" method="POST" class="space-y-3 w-full" id="newsletter-form">
                    @csrf
                    <div class="flex flex-col sm:flex-row gap-2 sm:gap-0">
                        <div class="flex-grow">
                            <input type="email" name="email" placeholder="Your email address" required
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-700 sm:rounded-l-lg rounded-lg sm:rounded-r-none text-white placeholder-gray-400 focus:outline-none focus:border-green-500 focus:ring-1 focus:ring-green-500 transition-all duration-200">
                        </div>
                        <button type="submit" class="w-full sm:w-auto bg-green-600 hover:bg-green-700 px-6 py-2 rounded-lg sm:rounded-l-none sm:rounded-r-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fas fa-paper-plane"></i>
                            <span class="ml-2 sm:hidden">Subscribe</span>
                        </button>
                    </div>
                    <div id="newsletter-message" class="text-sm hidden"></div>
                </form>

                <!-- Social Media Links -->
                <div class="space-y-4">
                    <h4 class="font-medium text-white">Follow Us</h4>
                    <div class="flex space-x-4">
                        @if(isset($siteSettings['social_facebook']))
                        <a href="{{ $siteSettings['social_facebook'] }}" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        @endif

                        @if(isset($siteSettings['social_twitter']))
                        <a href="{{ $siteSettings['social_twitter'] }}" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        @endif

                        @if(isset($siteSettings['social_linkedin']))
                        <a href="{{ $siteSettings['social_linkedin'] }}" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        @endif

                        @if(isset($siteSettings['social_instagram']))
                        <a href="{{ $siteSettings['social_instagram'] }}" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        @endif

                        @if(isset($siteSettings['social_youtube']))
                        <a href="{{ $siteSettings['social_youtube'] }}" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-gray-400 text-sm">
                    © {{ date('Y') }} {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}. All rights reserved.
                </div>

                <div class="flex items-center space-x-6 text-sm">
                    <a href="{{ route('privacy-policy') }}" class="text-gray-400 hover:text-green-400 transition-colors">Privacy Policy</a>
                    <a href="{{ route('terms-of-service') }}" class="text-gray-400 hover:text-green-400 transition-colors">Terms of Service</a>
                    <a href="{{ route('privacy-policy') }}" class="text-gray-400 hover:text-green-400 transition-colors">Cookie Policy</a>
                </div>

                <!-- Certifications/Trust Badges -->
                <div class="flex items-center space-x-4">
                    @if(isset($siteSettings['ssl_badge']) && $siteSettings['ssl_badge'])
                    <div class="flex items-center text-xs text-gray-400">
                        <i class="fas fa-lock mr-1 text-green-500"></i>
                        SSL Secured
                    </div>
                    @endif

                    @if(isset($siteSettings['iso_certified']) && $siteSettings['iso_certified'])
                    <div class="flex items-center text-xs text-gray-400">
                        <i class="fas fa-certificate mr-1 text-green-500"></i>
                        ISO Certified
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</footer>


<!-- Quote Modal is included in the main layout -->

<script>
// Newsletter subscription
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const messageDiv = document.getElementById('newsletter-message');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnContent = submitBtn.innerHTML;

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    submitBtn.disabled = true;
    messageDiv.classList.add('hidden');

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageDiv.textContent = data.message;
            messageDiv.className = 'text-sm text-green-400 mt-2';
            form.reset();
        } else {
            messageDiv.textContent = data.message || 'An error occurred. Please try again.';
            messageDiv.className = 'text-sm text-red-400 mt-2';
        }
        messageDiv.classList.remove('hidden');
    })
    .catch(error => {
        messageDiv.textContent = 'An error occurred. Please try again.';
        messageDiv.className = 'text-sm text-red-400 mt-2';
        messageDiv.classList.remove('hidden');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalBtnContent;
        submitBtn.disabled = false;
    });
});
</script>
