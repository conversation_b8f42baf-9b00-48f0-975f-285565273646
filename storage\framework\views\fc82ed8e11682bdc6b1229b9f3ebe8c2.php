<?php $__env->startSection('title', 'Page Not Found - 404'); ?>
<?php $__env->startSection('description', 'The page you are looking for could not be found. Return to our homepage or contact us for assistance.'); ?>

<?php $__env->startSection('content'); ?>
<!-- 404 Error Page -->
<section class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-green-50 flex items-center justify-center py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- 404 Animation -->
            <div class="mb-12">
                <div class="relative inline-block">
                    <!-- Large 404 Text -->
                    <h1 class="text-9xl md:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-green-600 to-blue-800 leading-none select-none">
                        404
                    </h1>
                    
                    <!-- Floating Elements -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="relative">
                            <!-- Shipping Box Icon -->
                            <div class="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center animate-bounce">
                                <i class="fas fa-box-open text-4xl text-green-600"></i>
                            </div>
                            
                            <!-- Floating Particles -->
                            <div class="absolute -top-4 -right-4 w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-green-500 rounded-full animate-ping animation-delay-300"></div>
                            <div class="absolute top-1/2 -right-8 w-1.5 h-1.5 bg-yellow-500 rounded-full animate-ping animation-delay-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Oops! Package Not Found
                </h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    It looks like this page has been shipped to another dimension! 
                    Don't worry, our logistics team is on it. Let's get you back on track.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <a href="<?php echo e(route('home')); ?>" 
                   class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold rounded-full hover:from-green-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-home mr-3"></i>
                    Return Home
                </a>
                
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-green-500 hover:text-green-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-box mr-3"></i>
                    Browse Products
                </a>
            </div>

            <!-- Quick Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Track Package -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-search-location text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Track Your Package</h3>
                    <p class="text-gray-600 mb-6">Enter your tracking number to find your shipment</p>
                    <a href="<?php echo e(route('tracking.index')); ?>" 
                       class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                        Track Now <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Get Quote -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-calculator text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Get Shipping Quote</h3>
                    <p class="text-gray-600 mb-6">Calculate shipping costs for your next delivery</p>
                    <button onclick="openQuoteModal()" 
                            class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                        Get Quote <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>

                <!-- Contact Support -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-headset text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Need Help?</h3>
                    <p class="text-gray-600 mb-6">Our support team is here to assist you</p>
                    <a href="<?php echo e(route('contact')); ?>" 
                       class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors">
                        Contact Us <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Still Need Help?</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Phone -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-phone text-green-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Call Us</h4>
                        <p class="text-gray-600">
                            <a href="tel:<?php echo e($siteSettings['contact_phone'] ?? '+****************'); ?>" 
                               class="hover:text-green-600 transition-colors">
                                <?php echo e($siteSettings['contact_phone'] ?? '+****************'); ?>

                            </a>
                        </p>
                    </div>

                    <!-- Email -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-envelope text-blue-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Email Us</h4>
                        <p class="text-gray-600">
                            <a href="mailto:<?php echo e($siteSettings['contact_email'] ?? '<EMAIL>'); ?>" 
                               class="hover:text-blue-600 transition-colors">
                                <?php echo e($siteSettings['contact_email'] ?? '<EMAIL>'); ?>

                            </a>
                        </p>
                    </div>

                    <!-- Address -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-map-marker-alt text-purple-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Visit Us</h4>
                        <p class="text-gray-600">
                            <?php echo e($siteSettings['company_address'] ?? '123 Logistics Ave, Business District, City 12345'); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .animation-delay-300 {
        animation-delay: 300ms;
    }
    
    .animation-delay-600 {
        animation-delay: 600ms;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    .animate-float {
        animation: float 3s ease-in-out infinite;
    }
</style>

<script>
    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on scroll
        const cards = document.querySelectorAll('.transform');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/errors/404.blade.php ENDPATH**/ ?>