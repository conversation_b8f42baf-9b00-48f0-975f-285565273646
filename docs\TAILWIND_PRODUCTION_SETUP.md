# 🎨 Tailwind CSS Production Setup

## ⚠️ Current Development Setup

The application currently uses Tailwind CSS via CDN for development convenience:

```html
<script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>
```

## 🚀 Production Recommendations

For production deployment, follow these steps to optimize Tailwind CSS:

### 1. Install Tailwind CSS

```bash
npm install -D tailwindcss @tailwindcss/forms @tailwindcss/typography @tailwindcss/aspect-ratio
npx tailwindcss init
```

### 2. Configure tailwind.config.js

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'heading': ['Poppins', 'system-ui', 'sans-serif'],
      },
      colors: {
        'earth': {
          100: '#f5f1eb',
          200: '#e8dcc6',
          300: '#dbc7a1',
          400: '#ceb27c',
          500: '#b08d57',
          600: '#9a7a4a',
          700: '#82653f',
          800: '#6b5034',
          900: '#543b29',
        }
      },
      maxWidth: {
        '8xl': '88rem',
        '9xl': '96rem',
      }
    },
    container: {
      center: true,
      padding: '1rem',
      screens: {
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1400px',
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
```

### 3. Create CSS Input File

Create `resources/css/app.css`:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.hero-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Additional custom styles... */
```

### 4. Build Process

Add to `package.json`:

```json
{
  "scripts": {
    "build-css": "tailwindcss -i ./resources/css/app.css -o ./public/css/app.css --watch",
    "build-css-prod": "tailwindcss -i ./resources/css/app.css -o ./public/css/app.css --minify"
  }
}
```

### 5. Update Layout

Replace CDN link in `resources/views/layouts/frontend.blade.php`:

```html
<!-- Replace this -->
<script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,line-clamp"></script>

<!-- With this -->
<link href="{{ asset('css/app.css') }}" rel="stylesheet">
```

### 6. Laravel Mix Integration (Optional)

If using Laravel Mix, add to `webpack.mix.js`:

```javascript
const mix = require('laravel-mix');

mix.js('resources/js/app.js', 'public/js')
   .postCss('resources/css/app.css', 'public/css', [
       require('tailwindcss'),
   ]);
```

## 📈 Benefits of Production Setup

1. **Smaller Bundle Size**: Only includes used CSS classes
2. **Better Performance**: No runtime CSS generation
3. **Offline Support**: No dependency on external CDN
4. **Custom Configuration**: Full control over Tailwind config
5. **Build Optimization**: Minification and purging

## 🔧 Current Status

- ✅ Development setup working with CDN
- ⚠️ Production optimization pending
- 📝 Configuration ready for implementation

## 📚 Resources

- [Tailwind CSS Installation Guide](https://tailwindcss.com/docs/installation)
- [Laravel Integration](https://tailwindcss.com/docs/guides/laravel)
- [Production Optimization](https://tailwindcss.com/docs/optimizing-for-production)
