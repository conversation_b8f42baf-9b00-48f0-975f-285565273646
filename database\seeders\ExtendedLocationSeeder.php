<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExtendedLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedAdditionalCountries();
        $this->seedAustralianStates();
        $this->seedGermanStates();
        $this->seedFrenchRegions();
        $this->seedItalianRegions();
        $this->seedSpanishRegions();
        $this->seedJapaneseRegions();
        $this->seedIndianStates();
        $this->seedBrazilianStates();
        $this->seedMexicanStates();
    }

    private function seedAdditionalCountries(): void
    {
        $countries = [
            // European Countries
            ['name' => 'Ireland', 'code' => 'IRL', 'iso2' => 'IE', 'phone_code' => '+353', 'currency' => 'EUR', 'flag' => '🇮🇪', 'sort_order' => 23],
            ['name' => 'Portugal', 'code' => 'PRT', 'iso2' => 'PT', 'phone_code' => '+351', 'currency' => 'EUR', 'flag' => '🇵🇹', 'sort_order' => 24],
            ['name' => 'Greece', 'code' => 'GRC', 'iso2' => 'GR', 'phone_code' => '+30', 'currency' => 'EUR', 'flag' => '🇬🇷', 'sort_order' => 25],
            ['name' => 'Poland', 'code' => 'POL', 'iso2' => 'PL', 'phone_code' => '+48', 'currency' => 'PLN', 'flag' => '🇵🇱', 'sort_order' => 26],
            ['name' => 'Czech Republic', 'code' => 'CZE', 'iso2' => 'CZ', 'phone_code' => '+420', 'currency' => 'CZK', 'flag' => '🇨🇿', 'sort_order' => 27],
            ['name' => 'Hungary', 'code' => 'HUN', 'iso2' => 'HU', 'phone_code' => '+36', 'currency' => 'HUF', 'flag' => '🇭🇺', 'sort_order' => 28],
            ['name' => 'Romania', 'code' => 'ROU', 'iso2' => 'RO', 'phone_code' => '+40', 'currency' => 'RON', 'flag' => '🇷🇴', 'sort_order' => 29],
            ['name' => 'Bulgaria', 'code' => 'BGR', 'iso2' => 'BG', 'phone_code' => '+359', 'currency' => 'BGN', 'flag' => '🇧🇬', 'sort_order' => 30],
            ['name' => 'Croatia', 'code' => 'HRV', 'iso2' => 'HR', 'phone_code' => '+385', 'currency' => 'EUR', 'flag' => '🇭🇷', 'sort_order' => 31],
            ['name' => 'Slovenia', 'code' => 'SVN', 'iso2' => 'SI', 'phone_code' => '+386', 'currency' => 'EUR', 'flag' => '🇸🇮', 'sort_order' => 32],
            ['name' => 'Slovakia', 'code' => 'SVK', 'iso2' => 'SK', 'phone_code' => '+421', 'currency' => 'EUR', 'flag' => '🇸🇰', 'sort_order' => 33],
            ['name' => 'Estonia', 'code' => 'EST', 'iso2' => 'EE', 'phone_code' => '+372', 'currency' => 'EUR', 'flag' => '🇪🇪', 'sort_order' => 34],
            ['name' => 'Latvia', 'code' => 'LVA', 'iso2' => 'LV', 'phone_code' => '+371', 'currency' => 'EUR', 'flag' => '🇱🇻', 'sort_order' => 35],
            ['name' => 'Lithuania', 'code' => 'LTU', 'iso2' => 'LT', 'phone_code' => '+370', 'currency' => 'EUR', 'flag' => '🇱🇹', 'sort_order' => 36],
            ['name' => 'Iceland', 'code' => 'ISL', 'iso2' => 'IS', 'phone_code' => '+354', 'currency' => 'ISK', 'flag' => '🇮🇸', 'sort_order' => 37],
            ['name' => 'Luxembourg', 'code' => 'LUX', 'iso2' => 'LU', 'phone_code' => '+352', 'currency' => 'EUR', 'flag' => '🇱🇺', 'sort_order' => 38],
            ['name' => 'Malta', 'code' => 'MLT', 'iso2' => 'MT', 'phone_code' => '+356', 'currency' => 'EUR', 'flag' => '🇲🇹', 'sort_order' => 39],
            ['name' => 'Cyprus', 'code' => 'CYP', 'iso2' => 'CY', 'phone_code' => '+357', 'currency' => 'EUR', 'flag' => '🇨🇾', 'sort_order' => 40],

            // Asian Countries
            ['name' => 'Singapore', 'code' => 'SGP', 'iso2' => 'SG', 'phone_code' => '+65', 'currency' => 'SGD', 'flag' => '🇸🇬', 'sort_order' => 41],
            ['name' => 'Hong Kong', 'code' => 'HKG', 'iso2' => 'HK', 'phone_code' => '+852', 'currency' => 'HKD', 'flag' => '🇭🇰', 'sort_order' => 42],
            ['name' => 'Taiwan', 'code' => 'TWN', 'iso2' => 'TW', 'phone_code' => '+886', 'currency' => 'TWD', 'flag' => '🇹🇼', 'sort_order' => 43],
            ['name' => 'Malaysia', 'code' => 'MYS', 'iso2' => 'MY', 'phone_code' => '+60', 'currency' => 'MYR', 'flag' => '🇲🇾', 'sort_order' => 44],
            ['name' => 'Thailand', 'code' => 'THA', 'iso2' => 'TH', 'phone_code' => '+66', 'currency' => 'THB', 'flag' => '🇹🇭', 'sort_order' => 45],
            ['name' => 'Indonesia', 'code' => 'IDN', 'iso2' => 'ID', 'phone_code' => '+62', 'currency' => 'IDR', 'flag' => '🇮🇩', 'sort_order' => 46],
            ['name' => 'Philippines', 'code' => 'PHL', 'iso2' => 'PH', 'phone_code' => '+63', 'currency' => 'PHP', 'flag' => '🇵🇭', 'sort_order' => 47],
            ['name' => 'Vietnam', 'code' => 'VNM', 'iso2' => 'VN', 'phone_code' => '+84', 'currency' => 'VND', 'flag' => '🇻🇳', 'sort_order' => 48],
            ['name' => 'Pakistan', 'code' => 'PAK', 'iso2' => 'PK', 'phone_code' => '+92', 'currency' => 'PKR', 'flag' => '🇵🇰', 'sort_order' => 49],
            ['name' => 'Bangladesh', 'code' => 'BGD', 'iso2' => 'BD', 'phone_code' => '+880', 'currency' => 'BDT', 'flag' => '🇧🇩', 'sort_order' => 50],
            ['name' => 'Sri Lanka', 'code' => 'LKA', 'iso2' => 'LK', 'phone_code' => '+94', 'currency' => 'LKR', 'flag' => '🇱🇰', 'sort_order' => 51],
            ['name' => 'Myanmar', 'code' => 'MMR', 'iso2' => 'MM', 'phone_code' => '+95', 'currency' => 'MMK', 'flag' => '🇲🇲', 'sort_order' => 52],
            ['name' => 'Cambodia', 'code' => 'KHM', 'iso2' => 'KH', 'phone_code' => '+855', 'currency' => 'KHR', 'flag' => '🇰🇭', 'sort_order' => 53],
            ['name' => 'Laos', 'code' => 'LAO', 'iso2' => 'LA', 'phone_code' => '+856', 'currency' => 'LAK', 'flag' => '🇱🇦', 'sort_order' => 54],

            // Middle East & Africa
            ['name' => 'United Arab Emirates', 'code' => 'ARE', 'iso2' => 'AE', 'phone_code' => '+971', 'currency' => 'AED', 'flag' => '🇦🇪', 'sort_order' => 55],
            ['name' => 'Saudi Arabia', 'code' => 'SAU', 'iso2' => 'SA', 'phone_code' => '+966', 'currency' => 'SAR', 'flag' => '🇸🇦', 'sort_order' => 56],
            ['name' => 'Qatar', 'code' => 'QAT', 'iso2' => 'QA', 'phone_code' => '+974', 'currency' => 'QAR', 'flag' => '🇶🇦', 'sort_order' => 57],
            ['name' => 'Kuwait', 'code' => 'KWT', 'iso2' => 'KW', 'phone_code' => '+965', 'currency' => 'KWD', 'flag' => '🇰🇼', 'sort_order' => 58],
            ['name' => 'Bahrain', 'code' => 'BHR', 'iso2' => 'BH', 'phone_code' => '+973', 'currency' => 'BHD', 'flag' => '🇧🇭', 'sort_order' => 59],
            ['name' => 'Oman', 'code' => 'OMN', 'iso2' => 'OM', 'phone_code' => '+968', 'currency' => 'OMR', 'flag' => '🇴🇲', 'sort_order' => 60],
            ['name' => 'Israel', 'code' => 'ISR', 'iso2' => 'IL', 'phone_code' => '+972', 'currency' => 'ILS', 'flag' => '🇮🇱', 'sort_order' => 61],
            ['name' => 'Jordan', 'code' => 'JOR', 'iso2' => 'JO', 'phone_code' => '+962', 'currency' => 'JOD', 'flag' => '🇯🇴', 'sort_order' => 62],
            ['name' => 'Lebanon', 'code' => 'LBN', 'iso2' => 'LB', 'phone_code' => '+961', 'currency' => 'LBP', 'flag' => '🇱🇧', 'sort_order' => 63],
            ['name' => 'Turkey', 'code' => 'TUR', 'iso2' => 'TR', 'phone_code' => '+90', 'currency' => 'TRY', 'flag' => '🇹🇷', 'sort_order' => 64],
            ['name' => 'Egypt', 'code' => 'EGY', 'iso2' => 'EG', 'phone_code' => '+20', 'currency' => 'EGP', 'flag' => '🇪🇬', 'sort_order' => 65],
            ['name' => 'South Africa', 'code' => 'ZAF', 'iso2' => 'ZA', 'phone_code' => '+27', 'currency' => 'ZAR', 'flag' => '🇿🇦', 'sort_order' => 66],
            ['name' => 'Nigeria', 'code' => 'NGA', 'iso2' => 'NG', 'phone_code' => '+234', 'currency' => 'NGN', 'flag' => '🇳🇬', 'sort_order' => 67],
            ['name' => 'Kenya', 'code' => 'KEN', 'iso2' => 'KE', 'phone_code' => '+254', 'currency' => 'KES', 'flag' => '🇰🇪', 'sort_order' => 68],
            ['name' => 'Ghana', 'code' => 'GHA', 'iso2' => 'GH', 'phone_code' => '+233', 'currency' => 'GHS', 'flag' => '🇬🇭', 'sort_order' => 69],
            ['name' => 'Morocco', 'code' => 'MAR', 'iso2' => 'MA', 'phone_code' => '+212', 'currency' => 'MAD', 'flag' => '🇲🇦', 'sort_order' => 70],

            // Americas
            ['name' => 'Argentina', 'code' => 'ARG', 'iso2' => 'AR', 'phone_code' => '+54', 'currency' => 'ARS', 'flag' => '🇦🇷', 'sort_order' => 71],
            ['name' => 'Chile', 'code' => 'CHL', 'iso2' => 'CL', 'phone_code' => '+56', 'currency' => 'CLP', 'flag' => '🇨🇱', 'sort_order' => 72],
            ['name' => 'Colombia', 'code' => 'COL', 'iso2' => 'CO', 'phone_code' => '+57', 'currency' => 'COP', 'flag' => '🇨🇴', 'sort_order' => 73],
            ['name' => 'Peru', 'code' => 'PER', 'iso2' => 'PE', 'phone_code' => '+51', 'currency' => 'PEN', 'flag' => '🇵🇪', 'sort_order' => 74],
            ['name' => 'Venezuela', 'code' => 'VEN', 'iso2' => 'VE', 'phone_code' => '+58', 'currency' => 'VES', 'flag' => '🇻🇪', 'sort_order' => 75],
            ['name' => 'Ecuador', 'code' => 'ECU', 'iso2' => 'EC', 'phone_code' => '+593', 'currency' => 'USD', 'flag' => '🇪🇨', 'sort_order' => 76],
            ['name' => 'Uruguay', 'code' => 'URY', 'iso2' => 'UY', 'phone_code' => '+598', 'currency' => 'UYU', 'flag' => '🇺🇾', 'sort_order' => 77],
            ['name' => 'Paraguay', 'code' => 'PRY', 'iso2' => 'PY', 'phone_code' => '+595', 'currency' => 'PYG', 'flag' => '🇵🇾', 'sort_order' => 78],
            ['name' => 'Bolivia', 'code' => 'BOL', 'iso2' => 'BO', 'phone_code' => '+591', 'currency' => 'BOB', 'flag' => '🇧🇴', 'sort_order' => 79],

            // Oceania
            ['name' => 'New Zealand', 'code' => 'NZL', 'iso2' => 'NZ', 'phone_code' => '+64', 'currency' => 'NZD', 'flag' => '🇳🇿', 'sort_order' => 80],
            ['name' => 'Fiji', 'code' => 'FJI', 'iso2' => 'FJ', 'phone_code' => '+679', 'currency' => 'FJD', 'flag' => '🇫🇯', 'sort_order' => 81],
            ['name' => 'Papua New Guinea', 'code' => 'PNG', 'iso2' => 'PG', 'phone_code' => '+675', 'currency' => 'PGK', 'flag' => '🇵🇬', 'sort_order' => 82],

            // Additional Asian Countries
            ['name' => 'Nepal', 'code' => 'NPL', 'iso2' => 'NP', 'phone_code' => '+977', 'currency' => 'NPR', 'flag' => '🇳🇵', 'sort_order' => 83],
            ['name' => 'Bhutan', 'code' => 'BTN', 'iso2' => 'BT', 'phone_code' => '+975', 'currency' => 'BTN', 'flag' => '🇧🇹', 'sort_order' => 84],
            ['name' => 'Maldives', 'code' => 'MDV', 'iso2' => 'MV', 'phone_code' => '+960', 'currency' => 'MVR', 'flag' => '🇲🇻', 'sort_order' => 85],
            ['name' => 'Brunei', 'code' => 'BRN', 'iso2' => 'BN', 'phone_code' => '+673', 'currency' => 'BND', 'flag' => '🇧🇳', 'sort_order' => 86],
            ['name' => 'Mongolia', 'code' => 'MNG', 'iso2' => 'MN', 'phone_code' => '+976', 'currency' => 'MNT', 'flag' => '🇲🇳', 'sort_order' => 87],
        ];

        foreach ($countries as $countryData) {
            Country::firstOrCreate(
                ['iso2' => $countryData['iso2']],
                $countryData
            );
        }
    }

    private function seedAustralianStates(): void
    {
        $australia = Country::where('iso2', 'AU')->first();
        if (!$australia) return;

        $states = [
            [
                'name' => 'New South Wales',
                'code' => 'NSW',
                'cities' => ['Sydney', 'Newcastle', 'Wollongong', 'Central Coast', 'Maitland', 'Albury', 'Wagga Wagga', 'Port Macquarie', 'Tamworth', 'Orange']
            ],
            [
                'name' => 'Victoria',
                'code' => 'VIC',
                'cities' => ['Melbourne', 'Geelong', 'Ballarat', 'Bendigo', 'Shepparton', 'Latrobe', 'Warrnambool', 'Wodonga', 'Traralgon', 'Mildura']
            ],
            [
                'name' => 'Queensland',
                'code' => 'QLD',
                'cities' => ['Brisbane', 'Gold Coast', 'Townsville', 'Cairns', 'Toowoomba', 'Rockhampton', 'Mackay', 'Bundaberg', 'Hervey Bay', 'Gladstone']
            ],
            [
                'name' => 'Western Australia',
                'code' => 'WA',
                'cities' => ['Perth', 'Fremantle', 'Rockingham', 'Mandurah', 'Bunbury', 'Kalgoorlie', 'Geraldton', 'Albany', 'Broome', 'Port Hedland']
            ],
            [
                'name' => 'South Australia',
                'code' => 'SA',
                'cities' => ['Adelaide', 'Mount Gambier', 'Whyalla', 'Murray Bridge', 'Port Lincoln', 'Port Pirie', 'Victor Harbor', 'Gawler', 'Port Augusta', 'Kadina']
            ],
            [
                'name' => 'Tasmania',
                'code' => 'TAS',
                'cities' => ['Hobart', 'Launceston', 'Devonport', 'Burnie', 'Ulverstone', 'Glenorchy', 'Clarence', 'Kingborough', 'Sorell', 'Brighton']
            ],
            [
                'name' => 'Australian Capital Territory',
                'code' => 'ACT',
                'cities' => ['Canberra', 'Gungahlin', 'Tuggeranong', 'Weston Creek', 'Belconnen']
            ],
            [
                'name' => 'Northern Territory',
                'code' => 'NT',
                'cities' => ['Darwin', 'Alice Springs', 'Palmerston', 'Katherine', 'Nhulunbuy', 'Tennant Creek']
            ]
        ];

        $this->createStatesAndCities($australia, $states);
    }

    private function seedGermanStates(): void
    {
        $germany = Country::where('iso2', 'DE')->first();
        if (!$germany) return;

        $states = [
            [
                'name' => 'Bavaria',
                'code' => 'BY',
                'cities' => ['Munich', 'Nuremberg', 'Augsburg', 'Würzburg', 'Regensburg', 'Ingolstadt', 'Fürth', 'Erlangen', 'Bayreuth', 'Bamberg']
            ],
            [
                'name' => 'North Rhine-Westphalia',
                'code' => 'NW',
                'cities' => ['Cologne', 'Düsseldorf', 'Dortmund', 'Essen', 'Duisburg', 'Bochum', 'Wuppertal', 'Bielefeld', 'Bonn', 'Münster']
            ],
            [
                'name' => 'Baden-Württemberg',
                'code' => 'BW',
                'cities' => ['Stuttgart', 'Mannheim', 'Karlsruhe', 'Freiburg', 'Heidelberg', 'Ulm', 'Heilbronn', 'Pforzheim', 'Reutlingen', 'Esslingen']
            ],
            [
                'name' => 'Lower Saxony',
                'code' => 'NI',
                'cities' => ['Hanover', 'Braunschweig', 'Oldenburg', 'Osnabrück', 'Wolfsburg', 'Göttingen', 'Salzgitter', 'Hildesheim', 'Wilhelmshaven', 'Peine']
            ],
            [
                'name' => 'Hesse',
                'code' => 'HE',
                'cities' => ['Frankfurt am Main', 'Wiesbaden', 'Kassel', 'Darmstadt', 'Offenbach', 'Hanau', 'Gießen', 'Marburg', 'Fulda', 'Rüsselsheim']
            ],
            [
                'name' => 'Saxony',
                'code' => 'SN',
                'cities' => ['Dresden', 'Leipzig', 'Chemnitz', 'Zwickau', 'Plauen', 'Görlitz', 'Freiberg', 'Bautzen', 'Pirna', 'Meißen']
            ]
        ];

        $this->createStatesAndCities($germany, $states);
    }

    private function seedFrenchRegions(): void
    {
        $france = Country::where('iso2', 'FR')->first();
        if (!$france) return;

        $states = [
            [
                'name' => 'Île-de-France',
                'code' => 'IDF',
                'cities' => ['Paris', 'Boulogne-Billancourt', 'Saint-Denis', 'Argenteuil', 'Montreuil', 'Créteil', 'Nanterre', 'Colombes', 'Aulnay-sous-Bois', 'Rueil-Malmaison']
            ],
            [
                'name' => 'Provence-Alpes-Côte d\'Azur',
                'code' => 'PAC',
                'cities' => ['Marseille', 'Nice', 'Toulon', 'Aix-en-Provence', 'Avignon', 'Antibes', 'Cannes', 'La Seyne-sur-Mer', 'Hyères', 'Arles']
            ],
            [
                'name' => 'Auvergne-Rhône-Alpes',
                'code' => 'ARA',
                'cities' => ['Lyon', 'Saint-Étienne', 'Grenoble', 'Villeurbanne', 'Clermont-Ferrand', 'Valence', 'Chambéry', 'Annecy', 'Bourg-en-Bresse', 'Roanne']
            ],
            [
                'name' => 'Nouvelle-Aquitaine',
                'code' => 'NAQ',
                'cities' => ['Bordeaux', 'Limoges', 'Poitiers', 'Pau', 'La Rochelle', 'Mérignac', 'Pessac', 'Bayonne', 'Angoulême', 'Niort']
            ],
            [
                'name' => 'Occitanie',
                'code' => 'OCC',
                'cities' => ['Toulouse', 'Montpellier', 'Nîmes', 'Perpignan', 'Béziers', 'Narbonne', 'Carcassonne', 'Albi', 'Tarbes', 'Castres']
            ],
            [
                'name' => 'Hauts-de-France',
                'code' => 'HDF',
                'cities' => ['Lille', 'Amiens', 'Tourcoing', 'Roubaix', 'Dunkerque', 'Calais', 'Villeneuve-d\'Ascq', 'Saint-Quentin', 'Beauvais', 'Compiègne']
            ]
        ];

        $this->createStatesAndCities($france, $states);
    }

    private function seedItalianRegions(): void
    {
        $italy = Country::where('iso2', 'IT')->first();
        if (!$italy) return;

        $states = [
            [
                'name' => 'Lombardy',
                'code' => 'LOM',
                'cities' => ['Milan', 'Brescia', 'Monza', 'Bergamo', 'Como', 'Varese', 'Pavia', 'Cremona', 'Mantua', 'Lecco']
            ],
            [
                'name' => 'Lazio',
                'code' => 'LAZ',
                'cities' => ['Rome', 'Latina', 'Aprilia', 'Viterbo', 'Guidonia Montecelio', 'Fiumicino', 'Tivoli', 'Anzio', 'Pomezia', 'Terracina']
            ],
            [
                'name' => 'Campania',
                'code' => 'CAM',
                'cities' => ['Naples', 'Salerno', 'Giugliano in Campania', 'Torre del Greco', 'Pozzuoli', 'Casoria', 'Castellammare di Stabia', 'Afragola', 'Cava de\' Tirreni', 'Battipaglia']
            ],
            [
                'name' => 'Sicily',
                'code' => 'SIC',
                'cities' => ['Palermo', 'Catania', 'Messina', 'Syracuse', 'Marsala', 'Gela', 'Ragusa', 'Trapani', 'Vittoria', 'Caltanissetta']
            ],
            [
                'name' => 'Veneto',
                'code' => 'VEN',
                'cities' => ['Venice', 'Verona', 'Padua', 'Vicenza', 'Treviso', 'Rovigo', 'Belluno', 'Chioggia', 'Bassano del Grappa', 'San Donà di Piave']
            ],
            [
                'name' => 'Piedmont',
                'code' => 'PIE',
                'cities' => ['Turin', 'Novara', 'Alessandria', 'Asti', 'Cuneo', 'Biella', 'Verbania', 'Vercelli', 'Casale Monferrato', 'Alba']
            ]
        ];

        $this->createStatesAndCities($italy, $states);
    }

    private function seedSpanishRegions(): void
    {
        $spain = Country::where('iso2', 'ES')->first();
        if (!$spain) return;

        $states = [
            [
                'name' => 'Madrid',
                'code' => 'MD',
                'cities' => ['Madrid', 'Móstoles', 'Alcalá de Henares', 'Fuenlabrada', 'Leganés', 'Getafe', 'Alcorcón', 'Torrejón de Ardoz', 'Parla', 'Alcobendas']
            ],
            [
                'name' => 'Catalonia',
                'code' => 'CT',
                'cities' => ['Barcelona', 'L\'Hospitalet de Llobregat', 'Badalona', 'Terrassa', 'Sabadell', 'Lleida', 'Tarragona', 'Mataró', 'Santa Coloma de Gramenet', 'Reus']
            ],
            [
                'name' => 'Andalusia',
                'code' => 'AN',
                'cities' => ['Seville', 'Málaga', 'Córdoba', 'Granada', 'Jerez de la Frontera', 'Almería', 'Huelva', 'Marbella', 'Dos Hermanas', 'Algeciras']
            ],
            [
                'name' => 'Valencia',
                'code' => 'VC',
                'cities' => ['Valencia', 'Alicante', 'Elche', 'Castellón de la Plana', 'Torrevieja', 'Orihuela', 'Gandia', 'Sagunto', 'Dénia', 'Benidorm']
            ],
            [
                'name' => 'Basque Country',
                'code' => 'PV',
                'cities' => ['Bilbao', 'Vitoria-Gasteiz', 'San Sebastián', 'Barakaldo', 'Getxo', 'Irun', 'Portugalete', 'Santurtzi', 'Basauri', 'Errenteria']
            ]
        ];

        $this->createStatesAndCities($spain, $states);
    }

    private function seedJapaneseRegions(): void
    {
        $japan = Country::where('iso2', 'JP')->first();
        if (!$japan) return;

        $states = [
            [
                'name' => 'Tokyo',
                'code' => 'TK',
                'cities' => ['Tokyo', 'Hachioji', 'Tachikawa', 'Musashino', 'Mitaka', 'Ome', 'Fuchu', 'Akishima', 'Chofu', 'Machida']
            ],
            [
                'name' => 'Osaka',
                'code' => 'OS',
                'cities' => ['Osaka', 'Sakai', 'Higashiosaka', 'Hirakata', 'Toyonaka', 'Suita', 'Takatsuki', 'Yao', 'Neyagawa', 'Kishiwada']
            ],
            [
                'name' => 'Kanagawa',
                'code' => 'KN',
                'cities' => ['Yokohama', 'Kawasaki', 'Sagamihara', 'Fujisawa', 'Chigasaki', 'Hiratsuka', 'Machida', 'Koganei', 'Yamato', 'Atsugi']
            ],
            [
                'name' => 'Aichi',
                'code' => 'AI',
                'cities' => ['Nagoya', 'Toyota', 'Okazaki', 'Ichinomiya', 'Kasugai', 'Anjo', 'Toyohashi', 'Nishio', 'Kariya', 'Komaki']
            ],
            [
                'name' => 'Hokkaido',
                'code' => 'HK',
                'cities' => ['Sapporo', 'Asahikawa', 'Hakodate', 'Kushiro', 'Tomakomai', 'Obihiro', 'Otaru', 'Kitami', 'Ebetsu', 'Muroran']
            ]
        ];

        $this->createStatesAndCities($japan, $states);
    }

    private function seedIndianStates(): void
    {
        $india = Country::where('iso2', 'IN')->first();
        if (!$india) return;

        $states = [
            [
                'name' => 'Maharashtra',
                'code' => 'MH',
                'cities' => ['Mumbai', 'Pune', 'Nagpur', 'Thane', 'Nashik', 'Aurangabad', 'Solapur', 'Amravati', 'Kolhapur', 'Sangli']
            ],
            [
                'name' => 'Karnataka',
                'code' => 'KA',
                'cities' => ['Bangalore', 'Mysore', 'Hubli', 'Mangalore', 'Belgaum', 'Gulbarga', 'Davanagere', 'Bellary', 'Bijapur', 'Shimoga']
            ],
            [
                'name' => 'Tamil Nadu',
                'code' => 'TN',
                'cities' => ['Chennai', 'Coimbatore', 'Madurai', 'Tiruchirappalli', 'Salem', 'Tirunelveli', 'Erode', 'Vellore', 'Thoothukudi', 'Dindigul']
            ],
            [
                'name' => 'Gujarat',
                'code' => 'GJ',
                'cities' => ['Ahmedabad', 'Surat', 'Vadodara', 'Rajkot', 'Bhavnagar', 'Jamnagar', 'Junagadh', 'Gandhinagar', 'Anand', 'Navsari']
            ],
            [
                'name' => 'West Bengal',
                'code' => 'WB',
                'cities' => ['Kolkata', 'Howrah', 'Durgapur', 'Asansol', 'Siliguri', 'Malda', 'Bardhaman', 'Baharampur', 'Habra', 'Kharagpur']
            ],
            [
                'name' => 'Delhi',
                'code' => 'DL',
                'cities' => ['New Delhi', 'Delhi', 'Gurgaon', 'Faridabad', 'Ghaziabad', 'Noida', 'Greater Noida']
            ]
        ];

        $this->createStatesAndCities($india, $states);
    }

    private function seedBrazilianStates(): void
    {
        $brazil = Country::where('iso2', 'BR')->first();
        if (!$brazil) return;

        $states = [
            [
                'name' => 'São Paulo',
                'code' => 'SP',
                'cities' => ['São Paulo', 'Guarulhos', 'Campinas', 'São Bernardo do Campo', 'Santo André', 'Osasco', 'Ribeirão Preto', 'Sorocaba', 'Santos', 'Mauá']
            ],
            [
                'name' => 'Rio de Janeiro',
                'code' => 'RJ',
                'cities' => ['Rio de Janeiro', 'São Gonçalo', 'Duque de Caxias', 'Nova Iguaçu', 'Niterói', 'Belford Roxo', 'São João de Meriti', 'Campos dos Goytacazes', 'Petrópolis', 'Volta Redonda']
            ],
            [
                'name' => 'Minas Gerais',
                'code' => 'MG',
                'cities' => ['Belo Horizonte', 'Uberlândia', 'Contagem', 'Juiz de Fora', 'Betim', 'Montes Claros', 'Ribeirão das Neves', 'Uberaba', 'Governador Valadares', 'Ipatinga']
            ],
            [
                'name' => 'Bahia',
                'code' => 'BA',
                'cities' => ['Salvador', 'Feira de Santana', 'Vitória da Conquista', 'Camaçari', 'Itabuna', 'Juazeiro', 'Lauro de Freitas', 'Ilhéus', 'Jequié', 'Teixeira de Freitas']
            ],
            [
                'name' => 'Paraná',
                'code' => 'PR',
                'cities' => ['Curitiba', 'Londrina', 'Maringá', 'Ponta Grossa', 'Cascavel', 'São José dos Pinhais', 'Foz do Iguaçu', 'Colombo', 'Guarapuava', 'Paranaguá']
            ]
        ];

        $this->createStatesAndCities($brazil, $states);
    }

    private function seedMexicanStates(): void
    {
        $mexico = Country::where('iso2', 'MX')->first();
        if (!$mexico) return;

        $states = [
            [
                'name' => 'Mexico City',
                'code' => 'CDMX',
                'cities' => ['Mexico City', 'Iztapalapa', 'Ecatepec', 'Guadalajara', 'Puebla', 'Tijuana', 'León', 'Juárez', 'Zapopan', 'Monterrey']
            ],
            [
                'name' => 'Jalisco',
                'code' => 'JAL',
                'cities' => ['Guadalajara', 'Zapopan', 'Tlaquepaque', 'Tonalá', 'Puerto Vallarta', 'Lagos de Moreno', 'Tepatitlán', 'El Salto', 'Tlajomulco']
            ],
            [
                'name' => 'Nuevo León',
                'code' => 'NL',
                'cities' => ['Monterrey', 'Guadalupe', 'San Nicolás de los Garza', 'Apodaca', 'General Escobedo', 'Santa Catarina', 'San Pedro Garza García', 'Cadereyta Jiménez']
            ],
            [
                'name' => 'Puebla',
                'code' => 'PUE',
                'cities' => ['Puebla', 'Tehuacán', 'San Martín Texmelucan', 'Atlixco', 'San Pedro Cholula', 'Amozoc', 'San Andrés Cholula', 'Huauchinango']
            ]
        ];

        $this->createStatesAndCities($mexico, $states);
    }

    private function createStatesAndCities(Country $country, array $states): void
    {
        foreach ($states as $index => $stateData) {
            $cities = $stateData['cities'] ?? [];
            unset($stateData['cities']);

            $state = State::firstOrCreate(
                [
                    'country_id' => $country->id,
                    'code' => $stateData['code']
                ],
                array_merge($stateData, [
                    'country_id' => $country->id,
                    'is_active' => true,
                    'sort_order' => $index + 1
                ])
            );

            foreach ($cities as $cityIndex => $cityName) {
                City::firstOrCreate(
                    [
                        'state_id' => $state->id,
                        'name' => $cityName
                    ],
                    [
                        'state_id' => $state->id,
                        'name' => $cityName,
                        'is_active' => true,
                        'sort_order' => $cityIndex + 1
                    ]
                );
            }
        }
    }
}
