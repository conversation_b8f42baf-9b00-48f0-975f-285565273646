<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @php
        $siteSettings = cache()->remember('site_settings', 3600, function () {
            try {
                return \App\Models\SiteSetting::pluck('value', 'key_name')->toArray();
            } catch (\Exception $e) {
                return [];
            }
        });
        $siteName = $siteSettings['site_name'] ?? config('app.name', 'Atrix Logistics');
    @endphp

    <title>Customer Login - {{ $siteName }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Vite Assets -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        .tab-button.active {
            background-color: white;
            color: #2563eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>

</head>
<body class="bg-gradient-to-br from-blue-600 to-purple-700 min-h-screen">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-6 text-center">
                <h2 class="text-2xl font-bold text-white">
                    <i class="fas fa-shipping-fast mr-2"></i>
                    Customer Login
                </h2>
                <p class="text-blue-100 mt-2">Access your account</p>
            </div>

            <!-- Tab Navigation -->
            <div class="bg-gray-50 px-6 py-4">
                <div class="flex space-x-1 bg-gray-200 rounded-lg p-1">
                    <button id="email-tab" class="tab-button active flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200">
                        <i class="fas fa-envelope mr-2"></i>
                        Email & Password
                    </button>
                    <button id="phone-tab" class="tab-button flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 text-gray-600 hover:text-gray-800">
                        <i class="fas fa-mobile-alt mr-2"></i>
                        Phone & OTP
                    </button>
                </div>
            </div>

            <!-- Form Body -->
            <div class="px-6 py-6">
                @if ($errors->any())
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                        <ul class="list-disc list-inside space-y-1">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if (session('status'))
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                        {{ session('status') }}
                    </div>
                @endif

                <!-- Email & Password Form -->
                <div id="email-form" class="tab-content active">
                    <form method="POST" action="{{ route('customer.login') }}">
                        @csrf
                        <input type="hidden" name="login_type" value="email">

                        <div class="mb-6">
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-envelope mr-1 text-gray-400"></i>
                                Email Address
                            </label>
                            <input id="email" type="email" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('email') border-red-500 @enderror">
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-lock mr-1 text-gray-400"></i>
                                Password
                            </label>
                            <input id="password" type="password" name="password" required autocomplete="current-password"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('password') border-red-500 @enderror">
                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-600">Remember me</span>
                            </label>
                        </div>

                        <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-0.5">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Login with Email
                        </button>
                    </form>
                </div>

                <!-- Phone & OTP Form -->
                <div id="phone-form" class="tab-content">
                    <form id="phone-login-form">
                        @csrf
                        <input type="hidden" name="login_type" value="phone">

                        <div class="mb-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-mobile-alt mr-1 text-gray-400"></i>
                                Phone Number
                            </label>
                            <input id="phone" type="tel" name="phone" value="{{ old('phone') }}" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors @error('phone') border-red-500 @enderror"
                                   placeholder="+****************">
                            @error('phone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6" id="otp-section" style="display: none;">
                            <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-key mr-1 text-gray-400"></i>
                                OTP Code
                            </label>
                            <input id="otp" type="text" name="otp" maxlength="6"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-center text-lg tracking-widest"
                                   placeholder="000000">
                            <p class="mt-1 text-sm text-gray-500">Enter the 6-digit code sent to your phone</p>
                        </div>

                        <button type="submit" id="phone-submit-btn" class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 hover:to-green-800 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 transform hover:-translate-y-0.5">
                            <i class="fas fa-mobile-alt mr-2"></i>
                            Send OTP
                        </button>
                    </form>
                </div>

                <!-- Additional Links -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="text-center space-y-4">
                        <div>
                            <a href="{{ route('customer.forgot-password') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <i class="fas fa-key mr-1"></i>
                                Forgot your password?
                            </a>
                        </div>

                        <div class="text-gray-600 text-sm">
                            Don't have an account?
                            <a href="{{ route('customer.register') }}" class="text-blue-600 hover:text-blue-800 font-medium ml-1">
                                <i class="fas fa-user-plus mr-1"></i>
                                Create Account
                            </a>
                        </div>

                        <div class="flex justify-center space-x-8 pt-4">
                            <a href="{{ route('tracking.index') }}" class="text-gray-500 hover:text-blue-600 text-sm">
                                <i class="fas fa-search-location mr-1"></i>
                                Track Package
                            </a>
                            <button onclick="openLiveChat()" class="text-gray-500 hover:text-blue-600 text-sm">
                                <i class="fas fa-headset mr-1"></i>
                                Contact Support
                            </button>
                        </div>

                        <div class="pt-4">
                            <small class="text-gray-500">
                                <i class="fas fa-shield-alt mr-1"></i>
                                Your data is secure and protected
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        document.getElementById('email-tab').addEventListener('click', function() {
            console.log('Email tab clicked');
            switchTab('email');
        });

        document.getElementById('phone-tab').addEventListener('click', function() {
            console.log('Phone tab clicked');
            switchTab('phone');
        });

        function switchTab(tab) {
            console.log('Switching to tab:', tab);

            // Remove active class from all tabs and content
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
                btn.classList.add('text-gray-600');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to selected tab and content
            if (tab === 'email') {
                document.getElementById('email-tab').classList.add('active');
                document.getElementById('email-tab').classList.remove('text-gray-600');
                document.getElementById('email-form').classList.add('active');
                console.log('Email tab activated');
            } else {
                document.getElementById('phone-tab').classList.add('active');
                document.getElementById('phone-tab').classList.remove('text-gray-600');
                document.getElementById('phone-form').classList.add('active');
                console.log('Phone tab activated');
            }
        }

        // Phone/OTP form handling
        document.getElementById('phone-login-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const phoneInput = document.getElementById('phone');
            const otpSection = document.getElementById('otp-section');
            const submitBtn = document.getElementById('phone-submit-btn');
            const otpInput = document.getElementById('otp');

            if (otpSection.style.display === 'none') {
                // First step: Send OTP
                const phone = phoneInput.value.trim();
                if (!phone) {
                    alert('Please enter your phone number');
                    return;
                }

                sendCustomerOtp(phone);
            } else {
                // Second step: Verify OTP and login
                const phone = phoneInput.value.trim();
                const otp = otpInput.value.trim();

                if (!otp || otp.length !== 6) {
                    alert('Please enter the 6-digit OTP code');
                    return;
                }

                verifyCustomerOtp(phone, otp);
            }
        });

        function sendCustomerOtp(phone) {
            const submitBtn = document.getElementById('phone-submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

            fetch('{{ route("customer.otp.send") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ phone: phone })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('otp-section').style.display = 'block';
                    submitBtn.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>Login with OTP';
                    submitBtn.classList.remove('from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
                    submitBtn.classList.add('from-blue-600', 'to-blue-700', 'hover:from-blue-700', 'hover:to-blue-800');
                    document.getElementById('otp').focus();
                } else {
                    alert(data.message || 'Failed to send OTP');
                }
            })
            .catch(error => {
                alert('Failed to send OTP. Please try again.');
            })
            .finally(() => {
                submitBtn.disabled = false;
            });
        }

        function verifyCustomerOtp(phone, otp) {
            const submitBtn = document.getElementById('phone-submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Verifying...';

            fetch('{{ route("customer.otp.verify") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ phone: phone, otp: otp })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Login successful! Redirecting...');
                    window.location.href = data.redirect_url || '{{ route("customer.dashboard") }}';
                } else {
                    alert(data.message || 'Invalid OTP');
                    document.getElementById('otp').value = '';
                    document.getElementById('otp').focus();
                }
            })
            .catch(error => {
                alert('Verification failed. Please try again.');
                document.getElementById('otp').value = '';
                document.getElementById('otp').focus();
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>Login with OTP';
            });
        }

        // Function to open live chat
        function openLiveChat() {
            // Check if live chat widget exists
            const chatWidget = document.getElementById('liveChatWidget');
            if (chatWidget) {
                // Trigger the chat toggle
                const chatToggleBtn = document.getElementById('chatToggleBtn');
                if (chatToggleBtn) {
                    chatToggleBtn.click();
                }
            } else {
                // Fallback to contact page
                window.location.href = '{{ route("contact") }}';
            }
        }
    </script>

    <!-- Live Chat Widget -->
    @include('components.live-chat-widget')
</body>
</html>
