<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Confirmation - {{ $career->title }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 40px 30px;
        }
        .reference-box {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .reference-number {
            font-size: 24px;
            font-weight: bold;
            color: #0ea5e9;
            margin: 10px 0;
            letter-spacing: 1px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .info-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        .info-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            color: #1f2937;
            font-size: 16px;
        }
        .next-steps {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .next-steps h3 {
            color: #92400e;
            margin: 0 0 15px 0;
            font-size: 18px;
        }
        .next-steps ul {
            margin: 0;
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
            color: #78350f;
        }
        .contact-info {
            background: #f3f4f6;
            padding: 25px;
            border-radius: 8px;
            margin: 30px 0;
        }
        .contact-info h3 {
            margin: 0 0 15px 0;
            color: #374151;
        }
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .contact-icon {
            width: 20px;
            height: 20px;
            color: #10b981;
        }
        .footer {
            background: #374151;
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        .footer p {
            margin: 0;
            opacity: 0.8;
        }
        .btn {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            .contact-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>✅ Application Received!</h1>
            <p>Thank you for applying to {{ $siteSettings['company_name'] ?? 'Atrix Logistics' }}</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear {{ $applicantName }},</p>
            
            <p>We have successfully received your application for the position of <strong>{{ $career->title }}</strong>. Your application is now being reviewed by our hiring team.</p>

            <!-- Reference Number -->
            <div class="reference-box">
                <p style="margin: 0; font-weight: 600; color: #374151;">Your Application Reference Number:</p>
                <div class="reference-number">{{ $referenceNumber }}</div>
                <p style="margin: 0; font-size: 14px; color: #6b7280;">Please keep this reference number for your records</p>
            </div>

            <!-- Application Details -->
            <h3 style="color: #374151; margin: 30px 0 15px 0;">Application Summary</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Position Applied For</div>
                    <div class="info-value">{{ $career->title }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Application Date</div>
                    <div class="info-value">{{ $application->created_at->format('F j, Y') }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Department</div>
                    <div class="info-value">{{ $career->department ?? 'General' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Location</div>
                    <div class="info-value">{{ $career->location ?? 'To be discussed' }}</div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🚀 What Happens Next?</h3>
                <ul>
                    <li><strong>Initial Review:</strong> Our HR team will review your application within 3-5 business days</li>
                    <li><strong>Screening:</strong> If your profile matches our requirements, we'll contact you for a preliminary screening</li>
                    <li><strong>Interview Process:</strong> Qualified candidates will be invited for interviews</li>
                    <li><strong>Final Decision:</strong> We'll notify you of our decision regardless of the outcome</li>
                </ul>
            </div>

            <p>We appreciate your interest in joining our team at {{ $siteSettings['company_name'] ?? 'Atrix Logistics' }}. We will keep you updated on the status of your application throughout the process.</p>

            <!-- Contact Information -->
            <div class="contact-info">
                <h3>Need Help or Have Questions?</h3>
                <div class="contact-grid">
                    <div class="contact-item">
                        <span class="contact-icon">📧</span>
                        <span>{{ $siteSettings['hr_email'] ?? $siteSettings['contact_email'] ?? '<EMAIL>' }}</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-icon">📞</span>
                        <span>{{ $siteSettings['hr_phone'] ?? $siteSettings['contact_phone'] ?? '+****************' }}</span>
                    </div>
                </div>
                <p style="margin: 15px 0 0 0; font-size: 14px; color: #6b7280;">
                    Please reference your application number <strong>{{ $referenceNumber }}</strong> in all communications.
                </p>
            </div>

            <p style="margin-top: 30px;">
                Best regards,<br>
                <strong>{{ $siteSettings['company_name'] ?? 'Atrix Logistics' }} Hiring Team</strong>
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ $siteSettings['company_name'] ?? 'Atrix Logistics' }}. All rights reserved.</p>
            <p style="margin-top: 10px; font-size: 14px;">
                {{ $siteSettings['company_address'] ?? '123 Logistics Ave, Business District, City 12345' }}
            </p>
        </div>
    </div>
</body>
</html>
