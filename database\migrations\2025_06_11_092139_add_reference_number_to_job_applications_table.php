<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            $table->string('reference_number')->nullable()->after('id');
        });

        // Generate reference numbers for existing applications
        $applications = \App\Models\JobApplication::whereNull('reference_number')->get();
        foreach ($applications as $application) {
            $application->reference_number = 'APP-' . strtoupper(uniqid()) . '-' . $application->id;
            $application->save();
        }

        // Now make the field unique
        Schema::table('job_applications', function (Blueprint $table) {
            $table->unique('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_applications', function (Blueprint $table) {
            $table->dropColumn('reference_number');
        });
    }
};
