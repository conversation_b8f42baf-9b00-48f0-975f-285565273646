@extends('layouts.frontend')

@section('title', 'Server Error - 500')
@section('description', 'We are experiencing technical difficulties. Our team has been notified and is working to resolve the issue.')

@section('content')
<!-- 500 Error Page -->
<section class="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- 500 Animation -->
            <div class="mb-12">
                <div class="relative inline-block">
                    <!-- Large 500 Text -->
                    <h1 class="text-9xl md:text-[12rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-red-600 via-orange-600 to-red-800 leading-none select-none">
                        500
                    </h1>
                    
                    <!-- Floating Elements -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="relative">
                            <!-- Server Error Icon -->
                            <div class="w-24 h-24 bg-white rounded-2xl shadow-2xl flex items-center justify-center animate-pulse">
                                <i class="fas fa-server text-4xl text-red-600"></i>
                            </div>
                            
                            <!-- Floating Particles -->
                            <div class="absolute -top-4 -right-4 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-orange-500 rounded-full animate-ping animation-delay-300"></div>
                            <div class="absolute top-1/2 -right-8 w-1.5 h-1.5 bg-yellow-500 rounded-full animate-ping animation-delay-600"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-12">
                <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Server Temporarily Unavailable
                </h2>
                <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                    We're experiencing some technical difficulties on our end. 
                    Our technical team has been notified and is working to resolve this issue as quickly as possible.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
                <a href="{{ route('home') }}" 
                   class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-orange-600 text-white font-semibold rounded-full hover:from-red-700 hover:to-orange-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-home mr-3"></i>
                    Return Home
                </a>
                
                <button onclick="window.location.reload()" 
                        class="inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-full border-2 border-gray-300 hover:border-red-500 hover:text-red-600 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                    <i class="fas fa-redo mr-3"></i>
                    Try Again
                </button>
            </div>

            <!-- Quick Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Status Updates -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-info-circle text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">System Status</h3>
                    <p class="text-gray-600 mb-6">Check our system status for real-time updates</p>
                    <a href="{{ route('contact') }}" 
                       class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors">
                        Check Status <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Contact Support -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-headset text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Need Help?</h3>
                    <p class="text-gray-600 mb-6">Our support team is here to assist you</p>
                    <a href="{{ route('contact') }}" 
                       class="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors">
                        Contact Support <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Alternative Access -->
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-mobile-alt text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Mobile Access</h3>
                    <p class="text-gray-600 mb-6">Try accessing our mobile-optimized site</p>
                    <a href="{{ route('home') }}" 
                       class="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors">
                        Mobile Site <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-2xl p-8 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-900 mb-6">Need Immediate Assistance?</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Phone -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-phone text-red-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Emergency Support</h4>
                        <p class="text-gray-600">
                            <a href="tel:{{ $siteSettings['contact_phone'] ?? '+****************' }}" 
                               class="hover:text-red-600 transition-colors">
                                {{ $siteSettings['contact_phone'] ?? '+****************' }}
                            </a>
                        </p>
                    </div>

                    <!-- Email -->
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-envelope text-orange-600"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Technical Support</h4>
                        <p class="text-gray-600">
                            <a href="mailto:{{ $siteSettings['support_email'] ?? '<EMAIL>' }}" 
                               class="hover:text-orange-600 transition-colors">
                                {{ $siteSettings['support_email'] ?? '<EMAIL>' }}
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .animation-delay-300 {
        animation-delay: 300ms;
    }
    
    .animation-delay-600 {
        animation-delay: 600ms;
    }
</style>

<script>
    // Auto-refresh after 30 seconds
    setTimeout(function() {
        if (confirm('Would you like to try refreshing the page?')) {
            window.location.reload();
        }
    }, 30000);

    // Add some interactive animations
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.transform');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    });
</script>
@endsection
