<?php $__env->startSection('title', $career->title . ' - Careers at ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('meta_description', Str::limit(strip_tags($career->description), 160)); ?>

<?php $__env->startSection('content'); ?>
    <!-- Career Header with Breadcrumbs -->
    <?php if (isset($component)) { $__componentOriginala9d931d4f11b4d2850df99e991db1dca = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala9d931d4f11b4d2850df99e991db1dca = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-hero','data' => ['title' => ''.e($career->title).'','subtitle' => ''.e($career->department ? $career->department . ' • ' : '').''.e($career->location).' • '.e(ucfirst(str_replace('-', ' ', $career->employment_type))).' • '.e(ucfirst($career->experience_level)).' Level','featured' => $career->is_featured,'breadcrumbs' => [
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers', 'url' => route('careers.index')],
            ['title' => $career->title]
        ],'gradient' => 'from-blue-600 to-green-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => ''.e($career->title).'','subtitle' => ''.e($career->department ? $career->department . ' • ' : '').''.e($career->location).' • '.e(ucfirst(str_replace('-', ' ', $career->employment_type))).' • '.e(ucfirst($career->experience_level)).' Level','featured' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($career->is_featured),'breadcrumbs' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            ['title' => 'Home', 'url' => route('home')],
            ['title' => 'Careers', 'url' => route('careers.index')],
            ['title' => $career->title]
        ]),'gradient' => 'from-blue-600 to-green-600']); ?>
        <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
            <?php if(!$career->isApplicationDeadlinePassed()): ?>
                <a href="<?php echo e(route('careers.apply', $career->slug)); ?>"
                   class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center">
                    Apply for This Position
                </a>
            <?php else: ?>
                <span class="bg-gray-500 text-white px-8 py-3 rounded-lg font-medium text-center cursor-not-allowed">
                    Applications Closed
                </span>
            <?php endif; ?>
            <a href="<?php echo e(route('careers.index')); ?>"
               class="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-lg font-medium transition-colors text-center border border-white/30">
                View All Positions
            </a>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $attributes = $__attributesOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__attributesOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala9d931d4f11b4d2850df99e991db1dca)): ?>
<?php $component = $__componentOriginala9d931d4f11b4d2850df99e991db1dca; ?>
<?php unset($__componentOriginala9d931d4f11b4d2850df99e991db1dca); ?>
<?php endif; ?>

    <!-- Career Details -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid lg:grid-cols-3 gap-8">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Job Description -->
                        <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                            <div class="prose prose-lg max-w-none text-gray-700">
                                <?php echo nl2br(e($career->description)); ?>

                            </div>
                        </div>

                        <!-- Responsibilities -->
                        <?php if($career->responsibilities): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Key Responsibilities</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->responsibilities)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Requirements -->
                        <?php if($career->requirements): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->requirements)); ?>

                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Benefits -->
                        <?php if($career->benefits): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">Benefits & Perks</h2>
                                <div class="prose prose-lg max-w-none text-gray-700">
                                    <?php echo nl2br(e($career->benefits)); ?>

                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Quick Info -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Position Details</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Salary:</span>
                                    <span class="font-medium text-green-600"><?php echo e($career->formatted_salary); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Type:</span>
                                    <span class="font-medium"><?php echo e(ucfirst(str_replace('-', ' ', $career->employment_type))); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Level:</span>
                                    <span class="font-medium"><?php echo e(ucfirst($career->experience_level)); ?></span>
                                </div>
                                <?php if($career->application_deadline): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Deadline:</span>
                                        <span class="font-medium text-red-600"><?php echo e($career->application_deadline->format('M d, Y')); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Required Skills -->
                        <?php if($career->required_skills && count($career->required_skills) > 0): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Required Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $career->required_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
                                            <?php echo e($skill); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Preferred Skills -->
                        <?php if($career->preferred_skills && count($career->preferred_skills) > 0): ?>
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4">Preferred Skills</h3>
                                <div class="flex flex-wrap gap-2">
                                    <?php $__currentLoopData = $career->preferred_skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                                            <?php echo e($skill); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Contact Info -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Information</h3>
                            <div class="space-y-3">
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                    <a href="mailto:<?php echo e($career->contact_email); ?>" class="text-blue-600 hover:text-blue-800">
                                        <?php echo e($career->contact_email); ?>

                                    </a>
                                </div>
                                <div class="flex items-center gap-3">
                                    <i class="fas fa-phone text-gray-400"></i>
                                    <a href="tel:<?php echo e($career->contact_phone); ?>" class="text-blue-600 hover:text-blue-800">
                                        <?php echo e($career->contact_phone); ?>

                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Apply Button (Sticky) -->
                        <?php if(!$career->isApplicationDeadlinePassed()): ?>
                            <div class="sticky top-4">
                                <a href="<?php echo e(route('careers.apply', $career->slug)); ?>" 
                                   class="block w-full bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium transition-colors text-center">
                                    Apply for This Position
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Positions -->
    <?php
        $relatedCareers = \App\Models\Career::active()
            ->where('id', '!=', $career->id)
            ->where(function($query) use ($career) {
                $query->where('department', $career->department)
                      ->orWhere('experience_level', $career->experience_level);
            })
            ->limit(3)
            ->get();
    ?>

    <?php if($relatedCareers->count() > 0): ?>
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Related Positions</h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $relatedCareers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedCareer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
                                <h3 class="text-xl font-bold text-gray-900 mb-2"><?php echo e($relatedCareer->title); ?></h3>
                                <div class="text-sm text-gray-600 mb-3">
                                    <?php echo e($relatedCareer->department); ?> • <?php echo e($relatedCareer->location); ?>

                                </div>
                                <p class="text-gray-700 mb-4 line-clamp-3">
                                    <?php echo e(Str::limit(strip_tags($relatedCareer->description), 100)); ?>

                                </p>
                                <a href="<?php echo e(route('careers.show', $relatedCareer->slug)); ?>" 
                                   class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-medium transition-colors">
                                    View Details
                                </a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/careers/show.blade.php ENDPATH**/ ?>