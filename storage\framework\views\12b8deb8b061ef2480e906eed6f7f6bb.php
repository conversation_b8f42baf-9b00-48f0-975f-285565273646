<?php $__env->startSection('title', 'Preferences'); ?>
<?php $__env->startSection('page-title', 'Account Preferences'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Notification Preferences
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('customer.profile.update-preferences')); ?>" id="preferencesForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <div class="mb-4">
                        <h6 class="mb-3">Email Notifications</h6>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="order_updates" name="preferences[email_order_updates]" <?php echo e($user->preferences && isset($user->preferences['email_order_updates']) && $user->preferences['email_order_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="order_updates">Order Updates</label>
                            <small class="form-text text-muted d-block">Receive email notifications about your order status changes.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="shipping_updates" name="preferences[email_shipping_updates]" <?php echo e($user->preferences && isset($user->preferences['email_shipping_updates']) && $user->preferences['email_shipping_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="shipping_updates">Shipping Updates</label>
                            <small class="form-text text-muted d-block">Receive email notifications about shipping status changes.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="delivery_updates" name="preferences[email_delivery_updates]" <?php echo e($user->preferences && isset($user->preferences['email_delivery_updates']) && $user->preferences['email_delivery_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="delivery_updates">Delivery Updates</label>
                            <small class="form-text text-muted d-block">Receive email notifications about delivery status changes.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="quote_updates" name="preferences[email_quote_updates]" <?php echo e($user->preferences && isset($user->preferences['email_quote_updates']) && $user->preferences['email_quote_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="quote_updates">Quote Updates</label>
                            <small class="form-text text-muted d-block">Receive email notifications when quotes are updated or responded to.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="marketing_emails" name="preferences[email_marketing]" <?php echo e($user->preferences && isset($user->preferences['email_marketing']) && $user->preferences['email_marketing'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="marketing_emails">Marketing Emails</label>
                            <small class="form-text text-muted d-block">Receive promotional emails, special offers, and newsletters.</small>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="mb-3">SMS Notifications</h6>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="sms_order_updates" name="preferences[sms_order_updates]" <?php echo e($user->preferences && isset($user->preferences['sms_order_updates']) && $user->preferences['sms_order_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="sms_order_updates">Order Updates</label>
                            <small class="form-text text-muted d-block">Receive SMS notifications about your order status changes.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="sms_shipping_updates" name="preferences[sms_shipping_updates]" <?php echo e($user->preferences && isset($user->preferences['sms_shipping_updates']) && $user->preferences['sms_shipping_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="sms_shipping_updates">Shipping Updates</label>
                            <small class="form-text text-muted d-block">Receive SMS notifications about shipping status changes.</small>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="sms_delivery_updates" name="preferences[sms_delivery_updates]" <?php echo e($user->preferences && isset($user->preferences['sms_delivery_updates']) && $user->preferences['sms_delivery_updates'] ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="sms_delivery_updates">Delivery Updates</label>
                            <small class="form-text text-muted d-block">Receive SMS notifications about delivery status changes.</small>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="mb-3">Display Preferences</h6>
                        <div class="mb-3">
                            <label for="date_format" class="form-label">Date Format</label>
                            <select class="form-select" id="date_format" name="preferences[date_format]">
                                <option value="MM/DD/YYYY" <?php echo e($user->preferences && isset($user->preferences['date_format']) && $user->preferences['date_format'] == 'MM/DD/YYYY' ? 'selected' : ''); ?>>MM/DD/YYYY (US)</option>
                                <option value="DD/MM/YYYY" <?php echo e($user->preferences && isset($user->preferences['date_format']) && $user->preferences['date_format'] == 'DD/MM/YYYY' ? 'selected' : ''); ?>>DD/MM/YYYY (UK/EU)</option>
                                <option value="YYYY-MM-DD" <?php echo e($user->preferences && isset($user->preferences['date_format']) && $user->preferences['date_format'] == 'YYYY-MM-DD' ? 'selected' : ''); ?>>YYYY-MM-DD (ISO)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="time_format" class="form-label">Time Format</label>
                            <select class="form-select" id="time_format" name="preferences[time_format]">
                                <option value="12" <?php echo e($user->preferences && isset($user->preferences['time_format']) && $user->preferences['time_format'] == '12' ? 'selected' : ''); ?>>12-hour (AM/PM)</option>
                                <option value="24" <?php echo e($user->preferences && isset($user->preferences['time_format']) && $user->preferences['time_format'] == '24' ? 'selected' : ''); ?>>24-hour</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="currency" class="form-label">Preferred Currency</label>
                            <select class="form-select" id="currency" name="preferences[currency]">
                                <option value="USD" <?php echo e($user->preferences && isset($user->preferences['currency']) && $user->preferences['currency'] == 'USD' ? 'selected' : ''); ?>>US Dollar (USD)</option>
                                <option value="EUR" <?php echo e($user->preferences && isset($user->preferences['currency']) && $user->preferences['currency'] == 'EUR' ? 'selected' : ''); ?>>Euro (EUR)</option>
                                <option value="GBP" <?php echo e($user->preferences && isset($user->preferences['currency']) && $user->preferences['currency'] == 'GBP' ? 'selected' : ''); ?>>British Pound (GBP)</option>
                                <option value="CAD" <?php echo e($user->preferences && isset($user->preferences['currency']) && $user->preferences['currency'] == 'CAD' ? 'selected' : ''); ?>>Canadian Dollar (CAD)</option>
                                <option value="AUD" <?php echo e($user->preferences && isset($user->preferences['currency']) && $user->preferences['currency'] == 'AUD' ? 'selected' : ''); ?>>Australian Dollar (AUD)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="weight_unit" class="form-label">Weight Unit</label>
                            <select class="form-select" id="weight_unit" name="preferences[weight_unit]">
                                <option value="kg" <?php echo e($user->preferences && isset($user->preferences['weight_unit']) && $user->preferences['weight_unit'] == 'kg' ? 'selected' : ''); ?>>Kilograms (kg)</option>
                                <option value="lb" <?php echo e($user->preferences && isset($user->preferences['weight_unit']) && $user->preferences['weight_unit'] == 'lb' ? 'selected' : ''); ?>>Pounds (lb)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="distance_unit" class="form-label">Distance Unit</label>
                            <select class="form-select" id="distance_unit" name="preferences[distance_unit]">
                                <option value="km" <?php echo e($user->preferences && isset($user->preferences['distance_unit']) && $user->preferences['distance_unit'] == 'km' ? 'selected' : ''); ?>>Kilometers (km)</option>
                                <option value="mi" <?php echo e($user->preferences && isset($user->preferences['distance_unit']) && $user->preferences['distance_unit'] == 'mi' ? 'selected' : ''); ?>>Miles (mi)</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="mb-3">Communication Preferences</h6>
                        <div class="mb-3">
                            <label for="preferred_contact" class="form-label">Preferred Contact Method</label>
                            <select class="form-select" id="preferred_contact" name="preferences[preferred_contact]">
                                <option value="email" <?php echo e($user->preferences && isset($user->preferences['preferred_contact']) && $user->preferences['preferred_contact'] == 'email' ? 'selected' : ''); ?>>Email</option>
                                <option value="phone" <?php echo e($user->preferences && isset($user->preferences['preferred_contact']) && $user->preferences['preferred_contact'] == 'phone' ? 'selected' : ''); ?>>Phone</option>
                                <option value="sms" <?php echo e($user->preferences && isset($user->preferences['preferred_contact']) && $user->preferences['preferred_contact'] == 'sms' ? 'selected' : ''); ?>>SMS</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="contact_frequency" class="form-label">Contact Frequency</label>
                            <select class="form-select" id="contact_frequency" name="preferences[contact_frequency]">
                                <option value="as_needed" <?php echo e($user->preferences && isset($user->preferences['contact_frequency']) && $user->preferences['contact_frequency'] == 'as_needed' ? 'selected' : ''); ?>>As Needed (Only Important Updates)</option>
                                <option value="daily" <?php echo e($user->preferences && isset($user->preferences['contact_frequency']) && $user->preferences['contact_frequency'] == 'daily' ? 'selected' : ''); ?>>Daily Digest</option>
                                <option value="weekly" <?php echo e($user->preferences && isset($user->preferences['contact_frequency']) && $user->preferences['contact_frequency'] == 'weekly' ? 'selected' : ''); ?>>Weekly Summary</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?php echo e(route('customer.profile.show')); ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Profile
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Save Preferences
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    About Preferences
                </h5>
            </div>
            <div class="card-body">
                <p>Customize your experience with Atrix Logistics by setting your preferences. These settings help us provide you with a personalized service tailored to your needs.</p>
                
                <div class="alert alert-info">
                    <i class="fas fa-bell me-2"></i>
                    <strong>Notification Settings</strong>
                    <p class="mb-0 small">Control how and when you receive updates about your shipments, orders, and account activity.</p>
                </div>
                
                <div class="alert alert-primary">
                    <i class="fas fa-globe me-2"></i>
                    <strong>Regional Settings</strong>
                    <p class="mb-0 small">Set your preferred date format, time format, currency, and measurement units.</p>
                </div>
                
                <div class="alert alert-success">
                    <i class="fas fa-envelope me-2"></i>
                    <strong>Communication Preferences</strong>
                    <p class="mb-0 small">Choose how you'd like us to contact you and how often you want to receive updates.</p>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Privacy Settings
                </h5>
            </div>
            <div class="card-body">
                <p>Your privacy is important to us. You can manage your data and privacy settings here.</p>
                
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('customer.profile.change-password')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-lock me-2"></i>
                        Change Password
                    </a>
                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                        <i class="fas fa-user-times me-2"></i>
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAccountModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Delete Account
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
                <p>Are you sure you want to delete your account? This will permanently erase all your data, including:</p>
                <ul>
                    <li>Personal information</li>
                    <li>Order history</li>
                    <li>Shipping addresses</li>
                    <li>Payment methods</li>
                    <li>Preferences and settings</li>
                </ul>
                <p>To confirm, please enter your password:</p>
                <form id="deleteAccountForm" method="POST" action="<?php echo e(route('customer.profile.delete')); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-2"></i>
                    Delete My Account
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.customer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/customer/profile/preferences.blade.php ENDPATH**/ ?>