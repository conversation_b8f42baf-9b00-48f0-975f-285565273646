@props([
    'name' => 'location',
    'countryValue' => '',
    'stateValue' => '',
    'cityValue' => '',
    'required' => false,
    'showCountry' => true,
    'showState' => true,
    'showCity' => true,
    'style' => 'bootstrap' // bootstrap or tailwind
])

@php
    $baseClasses = $style === 'tailwind' 
        ? 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
        : 'form-control';
    
    $labelClasses = $style === 'tailwind'
        ? 'block text-sm font-medium text-gray-700 mb-2'
        : 'form-label';
        
    $containerClasses = $style === 'tailwind'
        ? 'grid md:grid-cols-3 gap-6'
        : 'row';
        
    $colClasses = $style === 'tailwind'
        ? ''
        : 'col-md-4 mb-3';
@endphp

<div class="{{ $containerClasses }}" data-location-container="{{ $name }}">
    @if($showCountry)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_country" class="{{ $labelClasses }}">
            Country {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_country" 
                name="country" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="country"
                data-location-name="{{ $name }}">
            <option value="">Select Country</option>
        </select>
    </div>
    @endif
    
    @if($showState)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_state" class="{{ $labelClasses }}">
            State/Province {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_state" 
                name="state" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="state"
                data-location-name="{{ $name }}"
                disabled>
            <option value="">Select State/Province</option>
        </select>
    </div>
    @endif
    
    @if($showCity)
    <div class="{{ $colClasses }}">
        <label for="{{ $name }}_city" class="{{ $labelClasses }}">
            City {{ $required ? '*' : '' }}
        </label>
        <select id="{{ $name }}_city" 
                name="city" 
                class="{{ $baseClasses }}"
                {{ $required ? 'required' : '' }}
                data-location-type="city"
                data-location-name="{{ $name }}"
                disabled>
            <option value="">Select City</option>
        </select>
    </div>
    @endif
</div>

@push('styles')
@once
<link href="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/css/tom-select.css" rel="stylesheet">
<style>
.ts-wrapper.single .ts-control:after {
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900; 
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    right: 10px;
    pointer-events: none;
}

.ts-control {
    min-height: 44px;
    align-items: center;
    padding: 4px 8px;
    outline: 0;
}

.ts-wrapper.focus .ts-control {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Tailwind specific styles */
.ts-wrapper.tailwind-style .ts-control {
    border-color: #d1d5db;
}

.ts-wrapper.tailwind-style.focus .ts-control {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.ts-dropdown {
    border: 1px solid #0d6efd;
    border-radius: 3px;
}

.ts-dropdown:not(.multi) .active {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

/* Hide original select when TomSelect is active */
.tomselected {
    display: none !important;
}

/* Ensure TomSelect wrapper takes full width */
.ts-wrapper {
    width: 100% !important;
}
</style>
@endonce
@endpush

@push('scripts')
@once
<script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
@endonce
<script>
(function() {
    'use strict';

    // Create a unique identifier for this component instance
    const componentId = 'tom-select-{{ $name }}-' + Math.random().toString(36).substr(2, 9);

    function initializeTomSelect() {
        const containerSelector = '[data-location-container="{{ $name }}"]';
        const container = document.querySelector(containerSelector);
        if (!container) {
            console.warn('TomSelect container not found:', containerSelector);
            return;
        }

        // Check if already initialized
        if (container.hasAttribute('data-tom-select-initialized')) {
            console.log('TomSelect already initialized for:', containerSelector);
            return;
        }
        container.setAttribute('data-tom-select-initialized', 'true');

        console.log('Initializing TomSelect for:', containerSelector, 'with ID:', componentId);
    
    const countrySelect = container.querySelector('[data-location-type="country"]');
    const stateSelect = container.querySelector('[data-location-type="state"]');
    const citySelect = container.querySelector('[data-location-type="city"]');
    
    let countryTomSelect, stateTomSelect, cityTomSelect;
    
    // Add styling class for Tailwind
    if ('{{ $style }}' === 'tailwind') {
        container.classList.add('tailwind-style');
    }

    // Initialize country dropdown
    if (countrySelect && typeof TomSelect !== 'undefined') {
        // Check if already initialized
        if (countrySelect.tomselect) {
            console.log('Country TomSelect already initialized, skipping...');
            countryTomSelect = countrySelect.tomselect;
        } else {
            try {
                countryTomSelect = new TomSelect(countrySelect, {
            placeholder: 'Select Country',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text',
            preload: true,
            load: function(query, callback) {
                // Load all countries initially
                fetch('/api/locations/countries')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const options = data.data.map(country => ({
                                value: country.name,
                                text: country.name,
                                countryId: country.id
                            }));
                            callback(options);
                        } else {
                            callback();
                        }
                    })
                    .catch(() => callback());
            },
            onChange: function(value) {
                // Reset and disable dependent dropdowns
                if (stateTomSelect) {
                    stateTomSelect.clear();
                    stateTomSelect.clearOptions();
                    stateTomSelect.disable();
                }
                if (cityTomSelect) {
                    cityTomSelect.clear();
                    cityTomSelect.clearOptions();
                    cityTomSelect.disable();
                }

                if (value && stateSelect) {
                    // Get the country ID from the selected option
                    const selectedOption = countryTomSelect.options[value];
                    if (selectedOption && selectedOption.countryId) {
                        loadStates(selectedOption.countryId);
                    }
                }
            }
            });

                // Load initial countries
                countryTomSelect.load('');
            } catch (error) {
                console.error('Error initializing country TomSelect:', error);
            }
        }
    }
    
    // Initialize state dropdown
    if (stateSelect && typeof TomSelect !== 'undefined') {
        // Check if already initialized
        if (stateSelect.tomselect) {
            console.log('State TomSelect already initialized, skipping...');
            stateTomSelect = stateSelect.tomselect;
        } else {
            try {
                stateTomSelect = new TomSelect(stateSelect, {
            placeholder: 'Select State/Province',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text',
            onChange: function(value) {
                // Reset and disable city dropdown
                if (cityTomSelect) {
                    cityTomSelect.clear();
                    cityTomSelect.clearOptions();
                    cityTomSelect.disable();
                }

                if (value && citySelect) {
                    // Get the state ID from the selected option
                    const selectedOption = stateTomSelect.options[value];
                    if (selectedOption && selectedOption.stateId) {
                        loadCities(selectedOption.stateId);
                    }
                }
            }
                });
            } catch (error) {
                console.error('Error initializing state TomSelect:', error);
            }
        }
    }

    // Initialize city dropdown
    if (citySelect && typeof TomSelect !== 'undefined') {
        // Check if already initialized
        if (citySelect.tomselect) {
            console.log('City TomSelect already initialized, skipping...');
            cityTomSelect = citySelect.tomselect;
        } else {
            try {
                cityTomSelect = new TomSelect(citySelect, {
            placeholder: 'Select City',
            searchField: ['text'],
            valueField: 'value',
            labelField: 'text'
                });
            } catch (error) {
                console.error('Error initializing city TomSelect:', error);
            }
        }
    }
    
    function loadStates(countryId, initialStateValue = null) {
        if (!stateTomSelect) return;

        fetch(`/api/locations/countries/${countryId}/states`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stateTomSelect.clearOptions();
                    data.data.forEach(state => {
                        stateTomSelect.addOption({
                            value: state.name,
                            text: state.name,
                            stateId: state.id
                        });
                    });
                    stateTomSelect.enable();

                    // Set initial state value if provided
                    if (initialStateValue) {
                        setTimeout(() => {
                            stateTomSelect.setValue(initialStateValue);
                            // Load cities if city value is also provided
                            if (citySelect && '{{ $cityValue }}') {
                                const selectedState = data.data.find(s => s.name === initialStateValue);
                                if (selectedState) {
                                    loadCities(selectedState.id, '{{ $cityValue }}');
                                }
                            }
                        }, 100);
                    }
                }
            })
            .catch(console.error);
    }
    
    function loadCities(stateId, initialCityValue = null) {
        if (!cityTomSelect) return;

        fetch(`/api/locations/states/${stateId}/cities`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cityTomSelect.clearOptions();
                    data.data.forEach(city => {
                        cityTomSelect.addOption({
                            value: city.name,
                            text: city.name
                        });
                    });
                    cityTomSelect.enable();

                    // Set initial city value if provided
                    if (initialCityValue) {
                        setTimeout(() => {
                            cityTomSelect.setValue(initialCityValue);
                        }, 100);
                    }
                }
            })
            .catch(console.error);
    }
    
    // Set initial values if provided
    @if($countryValue)
        if (countryTomSelect) {
            // Wait for countries to load, then set initial value
            countryTomSelect.on('load', function() {
                setTimeout(() => {
                    countryTomSelect.setValue('{{ $countryValue }}');
                    // Trigger change to load states
                    if (stateSelect && '{{ $stateValue }}') {
                        const selectedOption = countryTomSelect.options['{{ $countryValue }}'];
                        if (selectedOption && selectedOption.countryId) {
                            loadStates(selectedOption.countryId, '{{ $stateValue }}');
                        }
                    }
                }, 100);
            });
        }
    @endif
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeTomSelect);
    } else {
        initializeTomSelect();
    }
})();
</script>
@endpush
