@extends('layouts.admin')

@section('title', 'Contact Messages')

@section('page-header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Contact Messages</h1>
            <p class="text-muted">Manage contact form submissions from your website</p>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Messages</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">New Messages</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['new'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-inbox fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Read Messages</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['read'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-eye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Replied</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['replied'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-reply fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.communications.contacts.index') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All Statuses</option>
                                <option value="new" {{ request('status') === 'new' ? 'selected' : '' }}>New</option>
                                <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>Read</option>
                                <option value="replied" {{ request('status') === 'replied' ? 'selected' : '' }}>Replied</option>
                                <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by name, email, subject, or message..." 
                                   value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">Filter</button>
                                <a href="{{ route('admin.communications.contacts.index') }}" class="btn btn-secondary">Clear</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Contacts Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Contact Messages</h6>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleBulkActions()">
                        <i class="fas fa-tasks"></i> Bulk Actions
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            @if($contacts->count() > 0)
                <!-- Bulk Actions (hidden by default) -->
                <div id="bulk-actions" class="mb-3" style="display: none;">
                    <form method="POST" action="{{ route('admin.communications.contacts.bulk-action') }}" id="bulk-form">
                        @csrf
                        <div class="row align-items-end">
                            <div class="col-md-4">
                                <select name="action" class="form-control" required>
                                    <option value="">Select Action</option>
                                    <option value="mark_read">Mark as Read</option>
                                    <option value="mark_replied">Mark as Replied</option>
                                    <option value="mark_closed">Mark as Closed</option>
                                    <option value="delete">Delete</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">Apply to Selected</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleBulkActions()">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th width="30">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($contacts as $contact)
                                <tr class="{{ $contact->status === 'new' ? 'table-warning' : '' }}">
                                    <td>
                                        <input type="checkbox" name="contacts[]" value="{{ $contact->id }}" class="contact-checkbox">
                                    </td>
                                    <td>
                                        <strong>{{ $contact->name }}</strong>
                                        @if($contact->phone)
                                            <br><small class="text-muted">{{ $contact->phone }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $contact->email }}</td>
                                    <td>
                                        {{ $contact->subject ?: 'No Subject' }}
                                        <br><small class="text-muted">{{ Str::limit($contact->message, 50) }}</small>
                                    </td>
                                    <td>
                                        <span class="badge text-dark {{ $contact->status_badge }}">
                                            {{ ucfirst($contact->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $contact->created_at->format('M j, Y') }}
                                        <br><small class="text-muted">{{ $contact->created_at->format('g:i A') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.communications.contacts.show', $contact) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form method="POST" action="{{ route('admin.communications.contacts.destroy', $contact) }}" 
                                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div>
                    {{ $contacts->appends(request()->query())->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                    <h5>No contact messages found</h5>
                    <p class="text-muted">Contact messages will appear here when visitors submit the contact form.</p>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleBulkActions() {
    const bulkActions = document.getElementById('bulk-actions');
    bulkActions.style.display = bulkActions.style.display === 'none' ? 'block' : 'none';
}

// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.contact-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk form submission
document.getElementById('bulk-form').addEventListener('submit', function(e) {
    const selectedContacts = document.querySelectorAll('.contact-checkbox:checked');
    if (selectedContacts.length === 0) {
        e.preventDefault();
        alert('Please select at least one contact.');
        return;
    }
    
    const action = this.querySelector('select[name="action"]').value;
    if (action === 'delete') {
        if (!confirm('Are you sure you want to delete the selected contacts?')) {
            e.preventDefault();
        }
    }
});
</script>
@endpush
@endsection
