<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\LocationController;

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Location API routes (public access for dropdowns)
Route::prefix('locations')->name('locations.')->group(function () {
    Route::get('countries', [LocationController::class, 'countries'])->name('countries');
    Route::get('states/{countryId}', [LocationController::class, 'states'])->name('states');
    Route::get('cities/{stateId}', [LocationController::class, 'cities'])->name('cities');
    Route::get('search', [LocationController::class, 'search'])->name('search');
});
