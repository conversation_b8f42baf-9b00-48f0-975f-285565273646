<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'title' => null,
    'description' => null,
    'keywords' => null,
    'image' => null,
    'url' => null,
    'type' => 'website',
    'robots' => null,
    'canonical' => null,
    'locale' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'title' => null,
    'description' => null,
    'keywords' => null,
    'image' => null,
    'url' => null,
    'type' => 'website',
    'robots' => null,
    'canonical' => null,
    'locale' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $seoService = app(\App\Services\SeoService::class);
    $siteSettings = cache()->remember('site_settings', 3600, function () {
        return \App\Models\SiteSetting::pluck('value', 'key_name')->toArray();
    });
    
    // Optimize title and description
    $optimizedTitle = $seoService->optimizeTitle(
        $title ?: ($siteSettings['site_name'] ?? config('app.name')),
        $siteSettings['site_name'] ?? config('app.name')
    );
    
    $optimizedDescription = $seoService->optimizeMetaDescription(
        $description ?: ($siteSettings['site_description'] ?? 'Professional logistics solutions')
    );
    
    // Generate Open Graph and Twitter Card tags
    $ogTags = $seoService->generateOpenGraphTags([
        'type' => $type,
        'title' => $optimizedTitle,
        'description' => $optimizedDescription,
        'image' => $image,
        'url' => $url ?: request()->url(),
        'locale' => $locale ?: app()->getLocale(),
    ]);
    
    $twitterTags = $seoService->generateTwitterCardTags([
        'title' => $optimizedTitle,
        'description' => $optimizedDescription,
        'image' => $image,
    ]);
    
    // Generate canonical URL
    $canonicalUrl = $canonical ?: $seoService->generateCanonicalUrl();
    
    // Generate robots tag
    $robotsTag = $robots ?: $seoService->generateRobotsTag();
?>

<!-- Basic Meta Tags -->
<title><?php echo e($optimizedTitle); ?></title>
<meta name="description" content="<?php echo e($optimizedDescription); ?>">
<?php if($keywords): ?>
<meta name="keywords" content="<?php echo e($keywords); ?>">
<?php endif; ?>
<meta name="robots" content="<?php echo e($robotsTag); ?>">
<meta name="author" content="<?php echo e($siteSettings['site_name'] ?? config('app.name')); ?>">

<!-- Canonical URL -->
<link rel="canonical" href="<?php echo e($canonicalUrl); ?>">

<!-- Open Graph Meta Tags -->
<?php $__currentLoopData = $ogTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $property => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<meta property="<?php echo e($property); ?>" content="<?php echo e($content); ?>">
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Twitter Card Meta Tags -->
<?php $__currentLoopData = $twitterTags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<meta name="<?php echo e($name); ?>" content="<?php echo e($content); ?>">
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<!-- Additional SEO Meta Tags -->
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="theme-color" content="<?php echo e($siteSettings['theme_color'] ?? '#1f2937'); ?>">
<meta name="msapplication-TileColor" content="<?php echo e($siteSettings['theme_color'] ?? '#1f2937'); ?>">

<!-- Favicon and Icons -->
<link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
<link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('apple-touch-icon.png')); ?>">
<link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('favicon-32x32.png')); ?>">
<link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('favicon-16x16.png')); ?>">
<link rel="manifest" href="<?php echo e(asset('site.webmanifest')); ?>">

<!-- Language and Locale -->
<meta http-equiv="content-language" content="<?php echo e($locale ?: app()->getLocale()); ?>">

<!-- Security Headers -->
<meta http-equiv="X-Content-Type-Options" content="nosniff">
<meta http-equiv="X-Frame-Options" content="DENY">
<meta http-equiv="X-XSS-Protection" content="1; mode=block">
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

<!-- DNS Prefetch for Performance -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//fonts.gstatic.com">
<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<?php /**PATH C:\Users\<USER>\Desktop\Web Devs\logistics\resources\views/components/seo-meta-tags.blade.php ENDPATH**/ ?>