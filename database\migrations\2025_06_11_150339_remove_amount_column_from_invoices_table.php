<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // Check if the column exists before trying to drop it to make the migration safer
            if (Schema::hasColumn('invoices', 'amount')) {
                $table->dropColumn('amount');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoices', function (Blueprint $table) {
            // If you want to be able to roll back, you'd re-add it.
            // However, if it was an erroneous column, you might not need a down method,
            // or you'd add it back as nullable or with a default if absolutely necessary for rollback.
            // For an erroneous column, often just dropping it is fine.
            // $table->decimal('amount', 10, 2)->nullable(); // Example if you needed to restore it
        });
    }
};
